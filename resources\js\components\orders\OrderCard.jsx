import React, { forwardRef } from "react";
import { Link } from "@inertiajs/react";
import useTranslations from "@/hooks/useTranslations";
import { formatDate } from "@/helpers/formatDate";

const OrderCard = forwardRef(({ order }, ref) => {
    const { translate } = useTranslations();

    const getStatusBadgeClass = (color) => {
        switch (color) {
            case "success":
                return "bg-green-100 text-green-800 border border-green-200";
            case "warning":
                return "bg-yellow-100 text-yellow-800 border border-yellow-200";
            case "danger":
                return "bg-red-100 text-red-800 border border-red-200";
            case "info":
                return "bg-blue-100 text-blue-800 border border-blue-200";
            default:
                return "bg-gray-100 text-gray-800 border border-gray-200";
        }
    };

    const getRestrictionBadgeClass = (type) => {
        switch (type) {
            case "age":
                return "bg-orange-100 text-orange-800 border border-orange-200";
            case "gender":
                return "bg-purple-100 text-purple-800 border border-purple-200";
            case "membership":
                return "bg-indigo-100 text-indigo-800 border border-indigo-200";
            default:
                return "bg-gray-100 text-gray-800 border border-gray-200";
        }
    };

    const formatTime = (timeString) => {
        if (!timeString) return null;
        try {
            return new Date(`1970-01-01T${timeString}`).toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch {
            return timeString;
        }
    };

    const formatCurrency = (amount, currency = 'USD') => {
        if (!amount) return 'N/A';
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    };

    return (
        <div ref={ref} className="overflow-hidden bg-white rounded-xl border border-gray-100 shadow-lg transition-all duration-300 hover:shadow-xl">
            {/* Header with Order Info and Status */}
            <div className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100">
                <div className="flex justify-between items-start">
                    <div className="flex-1">
                        <div className="flex gap-3 items-center mb-2">
                            <h2 className="text-xl font-bold text-gray-900">
                                {translate("order.order_number", "Order")} #{order.order_no}
                            </h2>
                            <span
                                className={`px-3 py-1 rounded-full text-xs font-semibold ${getStatusBadgeClass(order.status.color)}`}
                            >
                                {order.status.label}
                            </span>
                        </div>
                        <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                            <div className="flex gap-1 items-center">
                                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                </svg>
                                <span>{translate("order.created_at", "Created")}: {formatDate(order.created_at)}</span>
                            </div>
                            {order.purchase_date && (
                                <div className="flex gap-1 items-center">
                                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" />
                                    </svg>
                                    <span>{translate("order.purchased", "Purchased")}: {formatDate(order.purchase_date)}</span>
                                </div>
                            )}
                        </div>
                    </div>
                    <div className="text-right">
                        <div className="text-2xl font-bold text-gray-900">
                            {formatCurrency(order.total_price, order.currency)}
                        </div>
                        <div className="text-sm text-gray-500">
                            {order.quantity} {order.quantity === 1 ? translate("common.ticket", "ticket") : translate("common.tickets", "tickets")}
                        </div>
                    </div>
                </div>
            </div>

            <div className="p-6">
                <div className="flex flex-col gap-8 lg:flex-row">
                    {/* Event Image */}
                    <div className="flex-shrink-0 w-full lg:w-1/3">
                        <div className="relative group">
                            <figure className="overflow-hidden relative h-56 bg-gradient-to-br from-blue-100 to-indigo-200 rounded-lg shadow-md">
                                {order.ticket.event?.image ? (
                                    <img
                                        src={order.ticket.event.image}
                                        alt={
                                            order.ticket.event?.image_alt ||
                                            order.ticket.event?.name ||
                                            translate("common.event", "Event")
                                        }
                                        className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
                                    />
                                ) : (
                                    <div className="flex justify-center items-center h-full">
                                        <img
                                            className="w-32 h-auto opacity-60"
                                            src="/img/ticketgol-logo.png"
                                            alt={
                                                order.ticket.event?.name ||
                                                translate("common.event", "Event")
                                            }
                                        />
                                    </div>
                                )}
                                {order.ticket.event?.category && (
                                    <div className="absolute top-3 left-3">
                                        <span className="px-2 py-1 text-xs font-medium text-white bg-black bg-opacity-70 rounded-md">
                                            {order.ticket.event.category.translation?.name || order.ticket.event.category.name}
                                        </span>
                                    </div>
                                )}
                            </figure>
                        </div>
                    </div>

                    {/* Event & Ticket Details */}
                    <div className="flex-1 space-y-6">
                        {/* Event Information */}
                        <div>
                            <h3 className="flex gap-2 items-center mb-4 text-lg font-semibold text-gray-900">
                                <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                </svg>
                                {translate("order.event_details", "Event Details")}
                            </h3>
                            
                            <div className="p-4 bg-gray-50 rounded-lg">
                                <h4 className="mb-3 text-lg font-semibold text-gray-900">
                                    {order.ticket.event?.name || translate("common.not_available", "N/A")}
                                </h4>
                                
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    {order.ticket.event?.date && (
                                        <div className="flex gap-2 items-center">
                                            <svg className="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                            </svg>
                                            <div>
                                                <span className="text-sm text-gray-600">{translate("common.date", "Date")}:</span>
                                                <span className="ml-1 font-medium">{formatDate(order.ticket.event.date)}</span>
                                                {order.ticket.event?.time && (
                                                    <span className="ml-2 text-sm text-gray-600">
                                                        at {formatTime(order.ticket.event.time)}
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                    )}

                                    {order.ticket.event?.location && (
                                        <div className="flex gap-2 items-center">
                                            <svg className="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                                            </svg>
                                            <div>
                                                <span className="text-sm text-gray-600">{translate("common.location", "Location")}:</span>
                                                <span className="ml-1 font-medium">{order.ticket.event.location}</span>
                                            </div>
                                        </div>
                                    )}

                                    {order.ticket.event?.country && (
                                        <div className="flex gap-2 items-center">
                                            <svg className="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clipRule="evenodd" />
                                            </svg>
                                            <div>
                                                <span className="text-sm text-gray-600">{translate("common.country", "Country")}:</span>
                                                <span className="ml-1 font-medium">{order.ticket.event.country.translation?.name || order.ticket.event.country.name}</span>
                                            </div>
                                        </div>
                                    )}

                                    {order.ticket.event?.league && (
                                        <div className="flex gap-2 items-center">
                                            <svg className="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <div>
                                                <span className="text-sm text-gray-600">{translate("common.league", "League")}:</span>
                                                <span className="ml-1 font-medium">{order.ticket.event.league.translation?.name || order.ticket.event.league.name}</span>
                                            </div>
                                        </div>
                                    )}
                                </div>

                                {/* Teams Information */}
                                {(order.ticket.event?.home_club || order.ticket.event?.guest_club) && (
                                    <div className="pt-4 mt-4 border-t border-gray-200">
                                        <div className="flex gap-4 justify-center items-center">
                                            {order.ticket.event?.home_club && (
                                                <div className="text-center">
                                                    <div className="mb-1 text-sm text-gray-600">{translate("common.home", "Home")}</div>
                                                    <div className="font-semibold text-blue-600">
                                                        {order.ticket.event.home_club.translation?.name || order.ticket.event.home_club.name}
                                                    </div>
                                                </div>
                                            )}
                                            {order.ticket.event?.home_club && order.ticket.event?.guest_club && (
                                                <div className="text-2xl font-bold text-gray-400">VS</div>
                                            )}
                                            {order.ticket.event?.guest_club && (
                                                <div className="text-center">
                                                    <div className="mb-1 text-sm text-gray-600">{translate("common.guest", "Guest")}</div>
                                                    <div className="font-semibold text-red-600">
                                                        {order.ticket.event.guest_club.translation?.name || order.ticket.event.guest_club.name}
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Stadium Information */}
                        {order.ticket.event?.stadium && (
                            <div>
                                <h4 className="flex gap-2 items-center mb-3 font-semibold text-gray-900 text-md">
                                    <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z" clipRule="evenodd" />
                                    </svg>
                                    {translate("common.stadium", "Stadium")}
                                </h4>
                                <div className="p-3 bg-green-50 rounded-lg">
                                    <div className="mb-2 font-medium text-gray-900">
                                        {order.ticket.event.stadium.translation?.name || order.ticket.event.stadium.name}
                                    </div>
                                    {(order.ticket.event.stadium.address_line_1 || order.ticket.event.stadium.city) && (
                                        <div className="text-sm text-gray-600">
                                            {[order.ticket.event.stadium.address_line_1, order.ticket.event.stadium.city, order.ticket.event.stadium.country?.translation?.name || order.ticket.event.stadium.country?.name].filter(Boolean).join(", ")}
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}

                        {/* Ticket Information */}
                        <div>
                            <h4 className="flex gap-2 items-center mb-3 font-semibold text-gray-900 text-md">
                                <svg className="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2 6a2 2 0 012-2h12a2 2 0 012 2v2a2 2 0 100 4v2a2 2 0 01-2 2H4a2 2 0 01-2-2v-2a2 2 0 100-4V6z" />
                                </svg>
                                {translate("order.ticket_details", "Ticket Details")}
                            </h4>
                            
                            <div className="p-4 bg-purple-50 rounded-lg">
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <span className="text-sm text-gray-600">{translate("order.ticket_number", "Ticket")}:</span>
                                        <span className="ml-1 font-mono font-semibold">#{order.ticket.ticket_no}</span>
                                    </div>

                                    {order.ticket.type && (
                                        <div>
                                            <span className="text-sm text-gray-600">{translate("common.type", "Type")}:</span>
                                            <span className="ml-1 font-medium">{order.ticket.type.translation?.name || order.ticket.type.name}</span>
                                        </div>
                                    )}

                                    {order.ticket.sector && (
                                        <div>
                                            <span className="text-sm text-gray-600">{translate("common.sector", "Sector")}:</span>
                                            <span className="ml-1 font-medium">
                                                {order.ticket.sector.translation?.name || translate("common.unknown_sector", "Unknown Sector")}
                                            </span>
                                        </div>
                                    )}

                                    {(order.ticket.row || order.ticket.seat) && (
                                        <div>
                                            <span className="text-sm text-gray-600">{translate("common.seat", "Seat")}:</span>
                                            <span className="ml-1 font-medium">
                                                {[order.ticket.row && `Row ${order.ticket.row}`, order.ticket.seat && `Seat ${order.ticket.seat}`].filter(Boolean).join(", ")}
                                            </span>
                                        </div>
                                    )}

                                    {order.ticket.face_value && (
                                        <div>
                                            <span className="text-sm text-gray-600">{translate("ticket.face_value", "Face Value")}:</span>
                                            <span className="ml-1 font-medium">{formatCurrency(order.ticket.face_value, order.currency)}</span>
                                        </div>
                                    )}

                                    {order.ticket.price && (
                                        <div>
                                            <span className="text-sm text-gray-600">{translate("common.price", "Price")}:</span>
                                            <span className="ml-1 font-medium">{formatCurrency(order.ticket.price, order.currency)}</span>
                                        </div>
                                    )}
                                </div>

                                {order.ticket.translations && order.ticket.translations.length > 0 && (
                                    <div className="pt-4 mt-4 border-t border-purple-200">
                                        <span className="text-sm text-gray-600">{translate("common.description", "Description")}:</span>
                                        <p className="mt-1 text-sm text-gray-700">
                                            {order.ticket.translations.find(t => t.locale === document.documentElement.lang)?.description || 
                                             order.ticket.translations[0]?.description}
                                        </p>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Seller Information */}
                        {(order.seller_name || order.seller_email) && (
                            <div>
                                <h4 className="flex gap-2 items-center mb-3 font-semibold text-gray-900 text-md">
                                    <svg className="w-4 h-4 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                                    </svg>
                                    {translate("order.seller_info", "Seller Information")}
                                </h4>
                                <div className="p-3 bg-orange-50 rounded-lg">
                                    {order.seller_name && (
                                        <div className="mb-1">
                                            <span className="text-sm text-gray-600">{translate("common.name", "Name")}:</span>
                                            <span className="ml-1 font-medium">{order.seller_name}</span>
                                        </div>
                                    )}
                                    {order.seller_email && (
                                        <div>
                                            <span className="text-sm text-gray-600">{translate("common.email", "Email")}:</span>
                                            <span className="ml-1 font-medium">{order.seller_email}</span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}

                        {/* Payment Information */}
                        {order.transaction && (
                            <div>
                                <h4 className="flex gap-2 items-center mb-3 font-semibold text-gray-900 text-md">
                                    <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" />
                                    </svg>
                                    {translate("order.payment_info", "Payment Information")}
                                </h4>
                                <div className="p-3 bg-green-50 rounded-lg">
                                    <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
                                        {order.transaction.payment_method && (
                                            <div>
                                                <span className="text-sm text-gray-600">{translate("payment.method", "Method")}:</span>
                                                <span className="ml-1 font-medium capitalize">{order.transaction.payment_method}</span>
                                            </div>
                                        )}
                                        {order.transaction.paid_date && (
                                            <div>
                                                <span className="text-sm text-gray-600">{translate("payment.paid_date", "Paid Date")}:</span>
                                                <span className="ml-1 font-medium">{formatDate(order.transaction.paid_date)}</span>
                                            </div>
                                        )}
                                        {order.transaction.card_last_four && (
                                            <div>
                                                <span className="text-sm text-gray-600">{translate("payment.card", "Card")}:</span>
                                                <span className="ml-1 font-medium">**** **** **** {order.transaction.card_last_four}</span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Restrictions */}
                        {order.combined_restrictions && order.combined_restrictions.length > 0 && (
                            <div>
                                <h4 className="flex gap-2 items-center mb-3 font-semibold text-gray-900 text-md">
                                    <svg className="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                    </svg>
                                    {translate("order.restrictions", "Restrictions")}
                                </h4>
                                <div className="flex flex-wrap gap-2">
                                    {order.combined_restrictions.map((restriction, index) => (
                                        <span
                                            key={index}
                                            className={`px-3 py-1 rounded-full text-xs font-medium ${getRestrictionBadgeClass(restriction.type)}`}
                                            title={restriction.description}
                                        >
                                            {restriction.name}
                                        </span>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                {/* Footer with Actions */}
                <div className="flex flex-col gap-4 justify-between items-start pt-6 mt-6 border-t border-gray-200 sm:flex-row sm:items-center">
                    <div className="flex flex-wrap gap-4 items-center">
                        <Link
                            href={route("my-account.order-detail", { id: order.id })}
                            className="inline-flex gap-2 items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg transition-colors duration-200 hover:bg-blue-700"
                        >
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                                <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                            </svg>
                            {translate("order.view_details", "View Details")}
                        </Link>
                        
                        {order.status.color === 'success' && (
                            <button className="inline-flex gap-2 items-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg transition-colors duration-200 hover:bg-gray-200">
                                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                                </svg>
                                {translate("order.download_ticket", "Download Ticket")}
                            </button>
                        )}
                    </div>
                    
                    <div className="text-right">
                        <div className="mb-1 text-sm text-gray-500">
                            {translate("order.order_total", "Order Total")}
                        </div>
                        <div className="text-2xl font-bold text-gray-900">
                            {formatCurrency(order.total_price, order.currency)}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
});

OrderCard.displayName = 'OrderCard';

export default OrderCard;
