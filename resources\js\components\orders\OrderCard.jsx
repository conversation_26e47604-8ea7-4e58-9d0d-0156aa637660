import React, { forwardRef } from "react";
import { Link } from "@inertiajs/react";
import useTranslations from "@/hooks/useTranslations";
import { formatDate } from "@/helpers/formatDate";

const OrderCard = forwardRef(({ order }, ref) => {
    const { translate } = useTranslations();

    const getStatusBadgeClass = (color) => {
        switch (color) {
            case "success":
                return "bg-green-100 text-green-800 border border-green-200";
            case "warning":
                return "bg-yellow-100 text-yellow-800 border border-yellow-200";
            case "danger":
                return "bg-red-100 text-red-800 border border-red-200";
            case "info":
                return "bg-blue-100 text-blue-800 border border-blue-200";
            default:
                return "bg-gray-100 text-gray-800 border border-gray-200";
        }
    };



    const formatTime = (timeString) => {
        if (!timeString) return null;
        try {
            return new Date(`1970-01-01T${timeString}`).toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch {
            return timeString;
        }
    };

    const formatCurrency = (amount, currency = 'USD') => {
        if (!amount) return 'N/A';
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    };

    return (
        <div ref={ref} className="overflow-hidden bg-white rounded-xl border border-gray-100 shadow-sm transition-all duration-300 hover:shadow-md">
            {/* Header with Order Info and Status */}
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100">
                <div className="flex justify-between items-start">
                    <div className="flex-1">
                        <div className="flex gap-3 items-center mb-2">
                            <h3 className="text-lg font-bold text-gray-900">
                                {translate("order.order_number", "Order")} #{order.order_no}
                            </h3>
                            <span
                                className={`px-2 py-1 rounded-full text-xs font-semibold ${getStatusBadgeClass(order.status.color)}`}
                            >
                                {order.status.label}
                            </span>
                        </div>
                        <div className="text-sm text-gray-600">
                            <span>{translate("order.created_at", "Created")}: {formatDate(order.created_at)}</span>
                        </div>
                    </div>
                    <div className="text-right">
                        <div className="text-xl font-bold text-gray-900">
                            {formatCurrency(order.total_price)}
                        </div>
                        <div className="text-sm text-gray-500">
                            {order.quantity} {order.quantity === 1 ? translate("common.ticket", "ticket") : translate("common.tickets", "tickets")}
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="p-4">
                <div className="flex gap-4">
                    {/* Event Info */}
                    <div className="flex-1">
                        <h4 className="font-semibold text-gray-900 mb-2">
                            {order.ticket.event?.name || translate("common.not_available", "N/A")}
                        </h4>

                        <div className="space-y-1 text-sm text-gray-600">
                            {order.ticket.event?.date && (
                                <div className="flex items-center gap-2">
                                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                    </svg>
                                    <span>{formatDate(order.ticket.event.date)}</span>
                                    {order.ticket.event?.time && (
                                        <span className="text-gray-500">at {formatTime(order.ticket.event.time)}</span>
                                    )}
                                </div>
                            )}

                            {order.ticket.event?.stadium && (
                                <div className="flex items-center gap-2">
                                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                                    </svg>
                                    <span>{order.ticket.event.stadium.name}</span>
                                </div>
                            )}

                            {/* Teams */}
                            {(order.ticket.event?.home_club || order.ticket.event?.guest_club) && (
                                <div className="flex items-center gap-2 pt-1">
                                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span>
                                        {order.ticket.event.home_club?.name}
                                        {order.ticket.event.home_club && order.ticket.event.guest_club && ' vs '}
                                        {order.ticket.event.guest_club?.name}
                                    </span>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Ticket Info */}
                    <div className="text-right">
                        <div className="text-sm text-gray-600 mb-1">
                            {translate("order.ticket_number", "Ticket")} #{order.ticket.ticket_no}
                        </div>
                        {order.ticket.sector && (
                            <div className="text-sm text-gray-600">
                                {translate("common.sector", "Sector")}: {order.ticket.sector.name}
                            </div>
                        )}
                    </div>
                </div>
            </div>
            {/* Footer with Actions */}
            <div className="px-4 pb-4 border-t border-gray-100">
                <div className="flex justify-between items-center pt-3">
                    <Link
                        href={route("my-account.order-detail", { id: order.id })}
                        className="inline-flex gap-2 items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg transition-colors duration-200 hover:bg-blue-100"
                    >
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                            <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                        </svg>
                        {translate("order.view_details", "View Details")}
                    </Link>

                    {order.status.color === 'success' && (
                        <button className="inline-flex gap-2 items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg transition-colors duration-200 hover:bg-gray-200">
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                            {translate("order.download_ticket", "Download Ticket")}
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
});

OrderCard.displayName = 'OrderCard';

export default OrderCard;
