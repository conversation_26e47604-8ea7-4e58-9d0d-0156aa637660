import React, { forwardRef } from "react";
import { Link } from "@inertiajs/react";
import useTranslations from "@/hooks/useTranslations";
import { formatDate } from "@/helpers/formatDate";

const OrderCard = forwardRef(({ order }, ref) => {
    const { translate } = useTranslations();

    const getStatusBadgeClass = (color) => {
        switch (color) {
            case "success":
                return "bg-green-100 text-green-800 border border-green-200";
            case "warning":
                return "bg-yellow-100 text-yellow-800 border border-yellow-200";
            case "danger":
                return "bg-red-100 text-red-800 border border-red-200";
            case "info":
                return "bg-blue-100 text-blue-800 border border-blue-200";
            default:
                return "bg-gray-100 text-gray-800 border border-gray-200";
        }
    };



    const formatTime = (timeString) => {
        if (!timeString) return null;
        try {
            return new Date(`1970-01-01T${timeString}`).toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch {
            return timeString;
        }
    };

    const formatCurrency = (amount, currency = 'USD') => {
        if (!amount) return 'N/A';
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    };

    return (
        <div ref={ref} className="overflow-hidden bg-white rounded-xl border border-gray-200 shadow-sm transition-all duration-300 hover:shadow-lg hover:border-blue-200">
            {/* Header with Order Info and Status */}
            <div className="p-5 bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 border-b border-gray-200">
                <div className="flex justify-between items-start">
                    <div className="flex-1">
                        <div className="flex gap-3 items-center mb-3">
                            <div className="flex items-center gap-2">
                                <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" />
                                </svg>
                                <h3 className="text-lg font-bold text-gray-900">
                                    {translate("order.order_number", "Order")} #{order.order_no}
                                </h3>
                            </div>
                            <span
                                className={`px-3 py-1 rounded-full text-xs font-semibold ${getStatusBadgeClass(order.status.color)}`}
                            >
                                {order.status.label}
                            </span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                            </svg>
                            <span className="font-medium">{translate("order.created_at", "Created")}:</span>
                            <span>{formatDate(order.created_at)}</span>
                        </div>
                    </div>
                    <div className="text-right bg-white rounded-lg p-3 shadow-sm border border-gray-100">
                        <div className="text-xs text-gray-500 uppercase tracking-wide font-semibold mb-1">
                            {translate("order.total_amount", "Total Amount")}
                        </div>
                        <div className="text-2xl font-bold text-gray-900">
                            {formatCurrency(order.total_price)}
                        </div>
                        <div className="text-sm text-gray-600 mt-1">
                            {order.quantity} {order.quantity === 1 ? translate("common.ticket", "ticket") : translate("common.tickets", "tickets")}
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="p-5">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Event Information */}
                    <div className="lg:col-span-2">
                        <div className="mb-4">
                            <div className="flex items-center gap-2 mb-2">
                                <svg className="w-5 h-5 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                </svg>
                                <span className="text-sm font-semibold text-gray-700 uppercase tracking-wide">
                                    {translate("common.event_name", "Event Name")}
                                </span>
                            </div>
                            <h4 className="text-lg font-bold text-gray-900 leading-tight">
                                {order.ticket.event?.name || translate("common.not_available", "N/A")}
                            </h4>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* Date & Time */}
                            {order.ticket.event?.date && (
                                <div className="bg-gray-50 rounded-lg p-3">
                                    <div className="flex items-center gap-2 mb-1">
                                        <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                        </svg>
                                        <span className="text-xs font-semibold text-gray-600 uppercase tracking-wide">
                                            {translate("common.date_time", "Date & Time")}
                                        </span>
                                    </div>
                                    <div className="text-sm font-medium text-gray-900">
                                        {formatDate(order.ticket.event.date)}
                                        {order.ticket.event?.time && (
                                            <span className="text-gray-600 ml-2">
                                                at {formatTime(order.ticket.event.time)}
                                            </span>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* Stadium */}
                            {order.ticket.event?.stadium && (
                                <div className="bg-gray-50 rounded-lg p-3">
                                    <div className="flex items-center gap-2 mb-1">
                                        <svg className="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                                        </svg>
                                        <span className="text-xs font-semibold text-gray-600 uppercase tracking-wide">
                                            {translate("common.stadium", "Stadium")}
                                        </span>
                                    </div>
                                    <div className="text-sm font-medium text-gray-900">
                                        {order.ticket.event.stadium.name}
                                    </div>
                                </div>
                            )}

                            {/* Teams */}
                            {(order.ticket.event?.home_club || order.ticket.event?.guest_club) && (
                                <div className="bg-gradient-to-r from-blue-50 to-red-50 rounded-lg p-3 md:col-span-2">
                                    <div className="flex items-center gap-2 mb-2">
                                        <svg className="w-4 h-4 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <span className="text-xs font-semibold text-gray-600 uppercase tracking-wide">
                                            {translate("common.teams", "Teams")}
                                        </span>
                                    </div>
                                    <div className="flex items-center justify-center gap-4">
                                        {order.ticket.event.home_club && (
                                            <div className="text-center">
                                                <div className="text-xs text-gray-500 mb-1">{translate("common.home", "Home")}</div>
                                                <div className="font-semibold text-blue-700">
                                                    {order.ticket.event.home_club.name}
                                                </div>
                                            </div>
                                        )}
                                        {order.ticket.event.home_club && order.ticket.event.guest_club && (
                                            <div className="text-xl font-bold text-gray-400">VS</div>
                                        )}
                                        {order.ticket.event.guest_club && (
                                            <div className="text-center">
                                                <div className="text-xs text-gray-500 mb-1">{translate("common.guest", "Guest")}</div>
                                                <div className="font-semibold text-red-700">
                                                    {order.ticket.event.guest_club.name}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Ticket Information */}
                    <div className="lg:col-span-1">
                        <div className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-lg p-4 border border-indigo-100">
                            <div className="flex items-center gap-2 mb-3">
                                <svg className="w-5 h-5 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2 6a2 2 0 012-2h12a2 2 0 012 2v2a2 2 0 100 4v2a2 2 0 01-2 2H4a2 2 0 01-2-2v-2a2 2 0 100-4V6z" />
                                </svg>
                                <span className="text-sm font-semibold text-indigo-700 uppercase tracking-wide">
                                    {translate("common.ticket_info", "Ticket Info")}
                                </span>
                            </div>

                            <div className="space-y-3">
                                <div>
                                    <div className="text-xs text-gray-600 uppercase tracking-wide font-medium mb-1">
                                        {translate("order.ticket_number", "Ticket Number")}
                                    </div>
                                    <div className="text-sm font-mono font-bold text-gray-900 bg-white rounded px-2 py-1 border">
                                        #{order.ticket.ticket_no}
                                    </div>
                                </div>

                                {order.ticket.sector && (
                                    <div>
                                        <div className="text-xs text-gray-600 uppercase tracking-wide font-medium mb-1">
                                            {translate("common.sector", "Sector")}
                                        </div>
                                        <div className="text-sm font-semibold text-gray-900 bg-white rounded px-2 py-1 border">
                                            {order.ticket.sector.name}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {/* Footer with Actions */}
            <div className="px-5 pb-5 border-t border-gray-200 bg-gray-50">
                <div className="flex flex-col sm:flex-row gap-3 justify-between items-center pt-4">
                    <Link
                        href={route("my-account.order-detail", { id: order.id })}
                        className="inline-flex gap-2 items-center px-4 py-2.5 text-sm font-semibold text-white bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg transition-all duration-200 hover:from-blue-700 hover:to-indigo-700 hover:shadow-md transform hover:scale-105"
                    >
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                            <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                        </svg>
                        {translate("order.view_details", "View Details")}
                    </Link>

                    {order.status.color === 'success' && (
                        <button className="inline-flex gap-2 items-center px-4 py-2.5 text-sm font-semibold text-gray-700 bg-white border border-gray-300 rounded-lg transition-all duration-200 hover:bg-gray-50 hover:border-gray-400 hover:shadow-sm">
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                            {translate("order.download_ticket", "Download Ticket")}
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
});

OrderCard.displayName = 'OrderCard';

export default OrderCard;
