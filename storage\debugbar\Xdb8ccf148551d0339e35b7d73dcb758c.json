{"__meta": {"id": "Xdb8ccf148551d0339e35b7d73dcb758c", "datetime": "2025-06-18 02:01:08", "utime": **********.263902, "method": "GET", "uri": "/api/v1/orders/12", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 2, "messages": [{"message": "[02:01:08] LOG.info: Detected N+1 Query", "message_html": null, "is_string": false, "label": "info", "time": **********.26352, "xdebug_link": null, "collector": "log"}, {"message": "[02:01:08] LOG.info: Model: App\\Models\\Country\r\nRelation: App\\Models\\CountryTranslation\r\nNum-Called: 2\r\nCall-Stack:\r\n#39 \\app\\Repositories\\OrderRepository.php:128\r\n#40 \\app\\Services\\OrderService.php:51\r\n#41 \\app\\Http\\Controllers\\Api\\V1\\OrderController.php:31\r\n#42 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:47\r\n#43 \\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php:21\r\n#44 \\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php:35\r\n#45 \\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php:20\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:266\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:212\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:808\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:144\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.263739, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.976196, "end": **********.26392, "duration": 0.*****************, "duration_str": "288ms", "measures": [{"label": "Booting", "start": **********.976196, "relative_start": 0, "end": **********.026105, "relative_end": **********.026105, "duration": 0.*****************, "duration_str": "49.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.02612, "relative_start": 0.*****************, "end": **********.263922, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "238ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 6754232, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/orders/{id}", "middleware": "api, set-locale, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\V1\\OrderController@show", "namespace": null, "where": [], "as": "api.orders.show", "prefix": "api/v1/orders", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FOrderController.php&line=29\" onclick=\"\">app/Http/Controllers/Api/V1/OrderController.php:29-40</a>"}, "queries": {"nb_statements": 32, "nb_visible_statements": 32, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.06128, "accumulated_duration_str": "61.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'lozOkcXmaVGi8C7rcRTIGiWNVnpFNp5J1rBvucUG' limit 1", "type": "query", "params": [], "bindings": ["lozOkcXmaVGi8C7rcRTIGiWNVnpFNp5J1rBvucUG"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.031738, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 1.469}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "auth.session", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.035018, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 1.469, "width_percent": 1.697}, {"sql": "select `id`, `order_no`, `buyer_id`, `ticket_id`, `quantity`, `total_price`, `status`, `purchase_date`, `description` from `orders` where `buyer_id` = 7 and `id` = 12 and `orders`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 17, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.0407481, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 16, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 3.166, "width_percent": 3.035}, {"sql": "select `id`, `ticket_no`, `event_id`, `price`, `quantity`, `ticket_type`, `sector_id`, `face_value_price`, `ticket_rows`, `ticket_seats` from `tickets` where `tickets`.`id` in (8) and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 22, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.046768, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 6.201, "width_percent": 1.567}, {"sql": "select `id` from `stadium_sectors` where `stadium_sectors`.`id` in (45) and `stadium_sectors`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 27, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.051697, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 26, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 7.768, "width_percent": 1.305}, {"sql": "select `stadium_sector_id`, `name` from `stadium_sector_translations` where `locale` = 'en' and `stadium_sector_translations`.`stadium_sector_id` in (45)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 32, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.056067, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 9.073, "width_percent": 1.403}, {"sql": "select `id`, `date`, `time`, `timezone`, `category`, `country_id`, `home_club_id`, `guest_club_id`, `stadium_id`, `league_id` from `events` where `events`.`id` in (7) and `events`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 27, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.059542, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 26, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 10.477, "width_percent": 1.664}, {"sql": "select `id`, `event_id`, `name`, `description` from `event_translations` where `locale` = 'en' and `event_translations`.`event_id` in (7)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 32, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.063287, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 12.141, "width_percent": 1.338}, {"sql": "select `id` from `countries` where `countries`.`id` in (211) and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 32, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.0675602, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 13.479, "width_percent": 5.924}, {"sql": "select `country_id`, `name` from `country_translations` where `locale` = 'en' and `country_translations`.`country_id` in (211)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 37, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 40, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.0764961, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 19.403, "width_percent": 3.345}, {"sql": "select `id` from `clubs` where `clubs`.`id` in (1) and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 32, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.081585, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 22.748, "width_percent": 3.231}, {"sql": "select `club_id`, `name` from `club_translations` where `locale` = 'en' and `club_translations`.`club_id` in (1)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 37, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 40, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.087207, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 25.979, "width_percent": 3.949}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (1) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Club'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Club"], "hints": null, "show_copy": true, "backtrace": [{"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 37, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 40, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.0948088, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 29.928, "width_percent": 1.828}, {"sql": "select `id` from `clubs` where `clubs`.`id` in (5) and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 32, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.0992088, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 31.756, "width_percent": 0.946}, {"sql": "select `club_id`, `name` from `club_translations` where `locale` = 'en' and `club_translations`.`club_id` in (5)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 37, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 40, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.102055, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 32.702, "width_percent": 1.077}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (5) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Club'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Club"], "hints": null, "show_copy": true, "backtrace": [{"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 37, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 40, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.1049151, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 33.779, "width_percent": 1.322}, {"sql": "select `id`, `address_line_1`, `address_line_2`, `postcode`, `country_id` from `stadiums` where `stadiums`.`id` in (5) and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 32, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.1078262, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 35.101, "width_percent": 2.725}, {"sql": "select `stadium_id`, `name` from `stadium_translations` where `locale` = 'en' and `stadium_translations`.`stadium_id` in (5)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 37, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 40, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.112437, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 37.826, "width_percent": 2.154}, {"sql": "select * from `countries` where `countries`.`id` in (211) and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 37, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 40, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.117184, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 39.98, "width_percent": 1.142}, {"sql": "select `country_id`, `name` from `country_translations` where `locale` = 'en' and `country_translations`.`country_id` in (211)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 41, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 42, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 43, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 44, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 45, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.119336, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 41, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 41.123, "width_percent": 0.881}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (5) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Stadium'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Stadium"], "hints": null, "show_copy": true, "backtrace": [{"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 37, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 40, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.121024, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 42.004, "width_percent": 0.865}, {"sql": "select `id` from `leagues` where `leagues`.`id` in (4) and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 32, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.1231768, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 42.869, "width_percent": 2.954}, {"sql": "select `league_id`, `name` from `league_translations` where `locale` = 'en' and `league_translations`.`league_id` in (4)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 37, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 40, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.127574, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 45.822, "width_percent": 3.003}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (4) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\League'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\League"], "hints": null, "show_copy": true, "backtrace": [{"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 37, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 38, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 40, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.1319609, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 36, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 48.825, "width_percent": 1.289}, {"sql": "select `restrictions`.`id`, `restrictions`.`type`, `event_restrictions`.`event_id` as `pivot_event_id`, `event_restrictions`.`restriction_id` as `pivot_restriction_id` from `restrictions` inner join `event_restrictions` on `restrictions`.`id` = `event_restrictions`.`restriction_id` where `event_restrictions`.`event_id` in (7) and `restrictions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 30, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 31, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 34, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.135444, "duration": 0.00633, "duration_str": "6.33ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 30, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 50.114, "width_percent": 10.33}, {"sql": "select `restriction_id`, `name` from `restriction_translations` where `locale` = 'en' and `restriction_translations`.`restriction_id` in (6, 7, 9)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 35, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 36, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 37, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 39, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.145358, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 35, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 60.444, "width_percent": 6.576}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (7) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 32, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.1517632, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 67.02, "width_percent": 1.044}, {"sql": "select `restrictions`.`id`, `restrictions`.`type`, `ticket_restrictions`.`ticket_id` as `pivot_ticket_id`, `ticket_restrictions`.`restriction_id` as `pivot_restriction_id` from `restrictions` inner join `ticket_restrictions` on `restrictions`.`id` = `ticket_restrictions`.`restriction_id` where `ticket_restrictions`.`ticket_id` in (8) and `restrictions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 26, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.154517, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 25, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 68.065, "width_percent": 6.315}, {"sql": "select `restriction_id`, `name` from `restriction_translations` where `locale` = 'en' and `restriction_translations`.`restriction_id` in (3, 8)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 30, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, {"index": 31, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 51}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 31}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 34, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.160729, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:128", "source": {"index": 30, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=128", "ajax": false, "filename": "OrderRepository.php", "line": "128"}, "connection": "ticketgol", "explain": null, "start_percent": 74.38, "width_percent": 1.191}, {"sql": "select * from `ticket_translations` where `ticket_translations`.`ticket_id` = 8 and `ticket_translations`.`ticket_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": [8, "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Resources/OrderDetailResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Resources\\OrderDetailResource.php", "line": 57}, {"index": 25, "namespace": null, "name": "app/Http/Resources/OrderDetailResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Resources\\OrderDetailResource.php", "line": 44}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 30, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.203365, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "OrderDetailResource.php:57", "source": {"index": 22, "namespace": null, "name": "app/Http/Resources/OrderDetailResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Resources\\OrderDetailResource.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FResources%2FOrderDetailResource.php&line=57", "ajax": false, "filename": "OrderDetailResource.php", "line": "57"}, "connection": "ticketgol", "explain": null, "start_percent": 75.571, "width_percent": 5.646}, {"sql": "select * from `media` where `media`.`model_type` = 'App\\\\Models\\\\Event' and `media`.`model_id` = 7 and `media`.`model_id` is not null", "type": "query", "params": [], "bindings": ["App\\Models\\Event", 7], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/OrderDetailResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Resources\\OrderDetailResource.php", "line": 87}, {"index": 21, "namespace": null, "name": "app/Http/Resources/OrderDetailResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Resources\\OrderDetailResource.php", "line": 149}, {"index": 24, "namespace": null, "name": "app/Http/Resources/OrderDetailResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Resources\\OrderDetailResource.php", "line": 44}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.22643, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "OrderDetailResource.php:87", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/OrderDetailResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Resources\\OrderDetailResource.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FResources%2FOrderDetailResource.php&line=87", "ajax": false, "filename": "OrderDetailResource.php", "line": "87"}, "connection": "ticketgol", "explain": null, "start_percent": 81.217, "width_percent": 6.527}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiakxzVjRtb3lGbHd1NVJ2ckw2dDFPdDRMVUVoeDk5UlhobG9CWmo1UyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6NztzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJFYxd1I2NEdNUW9sVE1BcEUzd3pseWV3TDhQOWJzQUMzYWZaT2dJaGo5ZnJtQ3VxelkuLjVlIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czozOToiaHR0cDovL3RpY2tldGdvbC50ZXN0L215LWFjY291bnQvb3JkZXJzIjt9fQ==', `last_activity` = **********, `user_id` = 7, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'lozOkcXmaVGi8C7rcRTIGiWNVnpFNp5J1rBvucUG'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiakxzVjRtb3lGbHd1NVJ2ckw2dDFPdDRMVUVoeDk5UlhobG9CWmo1UyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6NztzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJFYxd1I2NEdNUW9sVE1BcEUzd3pseWV3TDhQOWJzQUMzYWZaT2dJaGo5ZnJtQ3VxelkuLjVlIjtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czozOToiaHR0cDovL3RpY2tldGdvbC50ZXN0L215LWFjY291bnQvb3JkZXJzIjt9fQ==", **********, 7, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "lozOkcXmaVGi8C7rcRTIGiWNVnpFNp5J1rBvucUG"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.234688, "duration": 0.00751, "duration_str": "7.51ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 87.745, "width_percent": 12.255}]}, "models": {"data": {"App\\Models\\Slug": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "App\\Models\\Restriction": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FRestriction.php&line=1", "ajax": false, "filename": "Restriction.php", "line": "?"}}, "App\\Models\\RestrictionTranslation": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FRestrictionTranslation.php&line=1", "ajax": false, "filename": "RestrictionTranslation.php", "line": "?"}}, "App\\Models\\Country": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\CountryTranslation": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountryTranslation.php&line=1", "ajax": false, "filename": "CountryTranslation.php", "line": "?"}}, "App\\Models\\Club": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FClub.php&line=1", "ajax": false, "filename": "Club.php", "line": "?"}}, "App\\Models\\ClubTranslation": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FClubTranslation.php&line=1", "ajax": false, "filename": "ClubTranslation.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Order": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "App\\Models\\Ticket": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FTicket.php&line=1", "ajax": false, "filename": "Ticket.php", "line": "?"}}, "App\\Models\\StadiumSector": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSector.php&line=1", "ajax": false, "filename": "StadiumSector.php", "line": "?"}}, "App\\Models\\StadiumSectorTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSectorTranslation.php&line=1", "ajax": false, "filename": "StadiumSectorTranslation.php", "line": "?"}}, "App\\Models\\Event": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\EventTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEventTranslation.php&line=1", "ajax": false, "filename": "EventTranslation.php", "line": "?"}}, "App\\Models\\Stadium": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadium.php&line=1", "ajax": false, "filename": "Stadium.php", "line": "?"}}, "App\\Models\\StadiumTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumTranslation.php&line=1", "ajax": false, "filename": "StadiumTranslation.php", "line": "?"}}, "App\\Models\\League": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FLeague.php&line=1", "ajax": false, "filename": "League.php", "line": "?"}}, "App\\Models\\LeagueTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FLeagueTranslation.php&line=1", "ajax": false, "filename": "LeagueTranslation.php", "line": "?"}}, "App\\Models\\TicketTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FTicketTranslation.php&line=1", "ajax": false, "filename": "TicketTranslation.php", "line": "?"}}}, "count": 35, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jLsV4moyFlwu5RvrL6t1Ot4LUEhx99RXhloBZj5S", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "password_hash_web": "$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/my-account/orders\"\n]"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f2e4cdc-6f2c-4088-9e3e-b3620ae21acd\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/orders/12", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"796 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; XSRF-TOKEN=eyJpdiI6ImRHOS9jbS92MFVZWHRxcURNdndSaVE9PSIsInZhbHVlIjoiY21lYUpxZS90dVVteXpuMmQybkNrNElzWjBKMUVmeGNaWlo4aWtUbzZFdVFGTzQyajdDc3N1cDhKK0k3Mzg4OWtzZ29La2JON0VIU09HMzBoQTA5VkRaQUpBTFpNUjlNRCtFeGJBMy9YTE4rRjZQaHM0VHVTMkRPc1ZOdlBiZmoiLCJtYWMiOiJmYjQ0NDMyOGJlODE1NjEwNGIzN2I0OTJhZTk2YmY5OTc5ZGVjMzE1MDhkYzFjMjEyZTUzYzU4NGQzMjM2MDM1IiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6Ik5VZU5ObzA3bXB6bzRRWDdDazk3T1E9PSIsInZhbHVlIjoiU2NYYkZzV1IxSEVuNUNXOC9BZkl6czhQTGF3dTV1VVBYR1NnUXdEcnFUdEhvREFKN2krTmx6YXJwN0R6SjdTOC9mZ01WOFlscVNiRDI2S1MxcFN5OCtIS2piSm9USU04Ujh1akJvVURLSE9yUldOS2VndkxVVXRmdko5UHgrQnoiLCJtYWMiOiI4ZDg1ZmVmNDU1MTRhMWQwNjM2OGY5NWM4N2M5ZWUwYmY5MTYyOTU4NWFlYjdiNjVmYWY0NTJmMjUyYmZjNWNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://ticketgol.test/my-account/orders/12</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImRHOS9jbS92MFVZWHRxcURNdndSaVE9PSIsInZhbHVlIjoiY21lYUpxZS90dVVteXpuMmQybkNrNElzWjBKMUVmeGNaWlo4aWtUbzZFdVFGTzQyajdDc3N1cDhKK0k3Mzg4OWtzZ29La2JON0VIU09HMzBoQTA5VkRaQUpBTFpNUjlNRCtFeGJBMy9YTE4rRjZQaHM0VHVTMkRPc1ZOdlBiZmoiLCJtYWMiOiJmYjQ0NDMyOGJlODE1NjEwNGIzN2I0OTJhZTk2YmY5OTc5ZGVjMzE1MDhkYzFjMjEyZTUzYzU4NGQzMjM2MDM1IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-268099472 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jLsV4moyFlwu5RvrL6t1Ot4LUEhx99RXhloBZj5S</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lozOkcXmaVGi8C7rcRTIGiWNVnpFNp5J1rBvucUG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-268099472\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1684252785 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 18 Jun 2025 02:01:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImVPNWhJajF2ZzgzenpNOUpLZkNHK2c9PSIsInZhbHVlIjoiZzlhaGgwM0tCNTFCdXdiaVluS3kwM2xYNFVRd010T3RYWlhZcUNNSjhMa213V3ZNZHdSdDJIM0NGK0dZME1IYU9wYzJKdFZVMExYY2xMY2srankvSXhnMkx4alE1amozSWJ0L2k1MThSTFk4Ukl2Z3hsOW9jMWdxU1dLbkJ6UHciLCJtYWMiOiJjYjJkZGJlY2M4ZmNlOTExY2I1NDY2OGI5MTFlYjk4NDU3MGU0NzU0NmYyYzQ0ZDk0YzNlN2FmYWQzZjFiN2RkIiwidGFnIjoiIn0%3D; expires=Wed, 18 Jun 2025 04:01:08 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6IktvSVhYUWlZSENkOGR6WWR4Z0puU1E9PSIsInZhbHVlIjoieCtIMGJ3d0ZwUlZSQlM1aEpjdzdSVjkxRlM4bUk4NUoxVDRIcW1CUXNHTGxWaHZDZWJTVWg2Z2hiQnpNenJYV1hlN2JQeTNMcjVNN3J6SWQ3Yk5uZ1dRRGN0alV6cjlEa3BkZjB1SGtNS2FuSUpERExwOUp4bTIwbFJDUkc4NnYiLCJtYWMiOiIxZjZmNmYyZGY1NGJiN2IzMjViNTFmYzgyYmRjN2FiOWE4N2Q2YjU3N2E3NThlY2FjMDE0MWU0ZmY3ZWQ5ODBmIiwidGFnIjoiIn0%3D; expires=Wed, 18 Jun 2025 04:01:08 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImVPNWhJajF2ZzgzenpNOUpLZkNHK2c9PSIsInZhbHVlIjoiZzlhaGgwM0tCNTFCdXdiaVluS3kwM2xYNFVRd010T3RYWlhZcUNNSjhMa213V3ZNZHdSdDJIM0NGK0dZME1IYU9wYzJKdFZVMExYY2xMY2srankvSXhnMkx4alE1amozSWJ0L2k1MThSTFk4Ukl2Z3hsOW9jMWdxU1dLbkJ6UHciLCJtYWMiOiJjYjJkZGJlY2M4ZmNlOTExY2I1NDY2OGI5MTFlYjk4NDU3MGU0NzU0NmYyYzQ0ZDk0YzNlN2FmYWQzZjFiN2RkIiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 04:01:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6IktvSVhYUWlZSENkOGR6WWR4Z0puU1E9PSIsInZhbHVlIjoieCtIMGJ3d0ZwUlZSQlM1aEpjdzdSVjkxRlM4bUk4NUoxVDRIcW1CUXNHTGxWaHZDZWJTVWg2Z2hiQnpNenJYV1hlN2JQeTNMcjVNN3J6SWQ3Yk5uZ1dRRGN0alV6cjlEa3BkZjB1SGtNS2FuSUpERExwOUp4bTIwbFJDUkc4NnYiLCJtYWMiOiIxZjZmNmYyZGY1NGJiN2IzMjViNTFmYzgyYmRjN2FiOWE4N2Q2YjU3N2E3NThlY2FjMDE0MWU0ZmY3ZWQ5ODBmIiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 04:01:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1684252785\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jLsV4moyFlwu5RvrL6t1Ot4LUEhx99RXhloBZj5S</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://ticketgol.test/my-account/orders</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}