<?php

namespace App\Repositories;

use App\DTO\OrderFilterDTO;
use App\Enums\OrderStatus;
use App\Models\Order;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class OrderRepository extends BaseRepository
{
    public function __construct(Order $model)
    {
        $this->model = $model;
    }

    public function createOrderWithAttendees($checkoutDTO, $ticket)
    {
        $user = Auth::user();

        $order = $this->model->create([
            'buyer_id' => $user->id,
            'ticket_id' => $ticket->id,
            'quantity' => $checkoutDTO->quantity,
            'total_price' => $checkoutDTO->quantity * $ticket->price,
            'status' => OrderStatus::PENDING,
            'created_by' => $user->id,
            'purchase_date' => now(),
        ]);

        $order->attendees()->createMany($checkoutDTO->attendees);

        return $order;
    }

    public function cancelOrder(Order $order)
    {
        return $this->update($order->id, [
            'status' => OrderStatus::CANCELED,
        ]);
    }

    public function expireOrder(Order $order)
    {
        return $this->update($order->id, [
            'status' => OrderStatus::EXPIRED,
        ]);
    }

    public function completeOrder(Order $order)
    {
        return $this->update($order->id, [
            'status' => OrderStatus::CONFIRMED,
            'purchase_date' => now(),
        ]);
    }

    public function getOrdersForUser($userId, OrderFilterDTO $filtersDTO)
    {
        $query = Order::with([
            'ticket:id,event_id,ticket_no,price,sector_id,event_id',
            'ticket.event:id,date,time,timezone,category,country_id,home_club_id,guest_club_id,stadium_id,league_id',
            'ticket.event.translation:id,event_id,name,description',
            'ticket.event.stadium:id,address_line_1,address_line_2,postcode,country_id',
            'ticket.event.stadium.country.translation:id,country_id,name',
            'ticket.event.stadium.translation:id,stadium_id,name',
            'ticket.event.league:id',
            'ticket.event.league.translation:id,league_id,name',
            'ticket.event.homeClub:id',
            'ticket.event.homeClub.translation:club_id,name',
            'ticket.event.guestClub:id',
            'ticket.event.guestClub.translation:club_id,name',
            'ticket.sector.translation:stadium_sector_id,name',
        ])->where('buyer_id', $userId);

        if ($filtersDTO->search) {
            $query->where(function ($q) use ($filtersDTO) {
                $q->where('order_no', 'like', '%' . $filtersDTO->search . '%')
                    ->orWhereHas('ticket', function ($q) use ($filtersDTO) {
                        $q->where('ticket_no', 'like', '%' . $filtersDTO->search . '%');
                    });
            });
        }

        if ($filtersDTO->status) {
            $query->where('status', $filtersDTO->status);
        }

        if ($filtersDTO->dateFrom) {
            $query->whereDate('created_at', '>=', $filtersDTO->dateFrom);
        }

        if ($filtersDTO->dateTo) {
            $query->whereDate('created_at', '<=', $filtersDTO->dateTo);
        }

        return $query->orderBy('created_at', 'desc');
    }

    public function getOrderWithDetails($userId, $orderId)
    {
        $order = Order::with(
            [
                'ticket:id,ticket_no,event_id,price,quantity,ticket_type,sector_id,face_value_price,ticket_rows,ticket_seats',
                'ticket.sector:id',
                'ticket.sector.translation:stadium_sector_id,name',
                'ticket.event:id,date,time,timezone,category,country_id,home_club_id,guest_club_id,stadium_id,league_id',
                'ticket.event.translation:id,event_id,name,description',
                'ticket.event.country:id',
                'ticket.event.country.translation:country_id,name',
                'ticket.event.homeClub:id',
                'ticket.event.homeClub.translation:club_id,name',
                'ticket.event.guestClub:id',
                'ticket.event.guestClub.translation:club_id,name',
                'ticket.event.stadium:id,address_line_1,address_line_2,postcode,country_id',
                'ticket.event.stadium.translation:stadium_id,name',
                'ticket.event.stadium.country.translation:country_id,name',
                'ticket.event.league:id',
                'ticket.event.league.translation:league_id,name',
                'ticket.event.restrictions:id,type',
                'ticket.event.restrictions.translation:restriction_id,name',
                'ticket.restrictions:id,type',
                'ticket.restrictions.translation:restriction_id,name',
            ]
        )
            ->select(['id', 'order_no', 'buyer_id', 'ticket_id', 'quantity', 'total_price', 'status', 'purchase_date', 'description'])
            ->where('buyer_id', $userId)
            ->where('id', $orderId)
            ->first();

        return $order;
    }
}
