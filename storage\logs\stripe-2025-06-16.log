[2025-06-16 14:32:46] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 14:36:04] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 14:36:04] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 14:36:04] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 14:36:04] local.INFO: Expired records status updated to Expired  
[2025-06-16 14:36:04] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 14:36:04] local.INFO: Cron job executed {"time":"2025-06-16 14:36:04"} 
[2025-06-16 15:06:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:06:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:06:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:06:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:06:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:06:01] local.INFO: Cron job executed {"time":"2025-06-16 15:06:01"} 
[2025-06-16 15:07:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:07:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:07:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:07:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:07:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:07:01] local.INFO: Cron job executed {"time":"2025-06-16 15:07:01"} 
[2025-06-16 15:08:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:08:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:08:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:08:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:08:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:08:01] local.INFO: Cron job executed {"time":"2025-06-16 15:08:01"} 
[2025-06-16 15:09:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:09:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:09:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:09:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:09:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:09:01] local.INFO: Cron job executed {"time":"2025-06-16 15:09:01"} 
[2025-06-16 15:09:10] local.INFO: Charge succeeded {"charge":{"Stripe\\Charge":{"id":"ch_3RaeemRhkfMMoe7t0HrXAo7F","object":"charge","amount":54839,"amount_captured":54839,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":null,"billing_details":{"address":{"city":null,"country":"IN","line1":null,"line2":null,"postal_code":null,"state":null},"email":"<EMAIL>","name":"asdfg","phone":null,"tax_id":null},"calculated_statement_descriptor":"Stripe","captured":true,"created":1750086548,"currency":"eur","customer":"cus_SVg0s9fFjkckT4","description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"livemode":false,"metadata":[],"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":46,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3RaeemRhkfMMoe7t0nrqhFpI","payment_method":"pm_1RaeelRhkfMMoe7tqvLYspAJ","payment_method_details":{"card":{"amount_authorized":54839,"authorization_code":"734524","brand":"visa","checks":{"address_line1_check":null,"address_postal_code_check":null,"cvc_check":"pass"},"country":"US","exp_month":3,"exp_year":2045,"extended_authorization":{"status":"disabled"},"fingerprint":"4qHsohpy0cF1sMdU","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"521137211511110","overcapture":{"maximum_amount_capturable":54839,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"presentment_details":{"presentment_amount":5672229,"presentment_currency":"inr"},"radar_options":[],"receipt_email":null,"receipt_number":null,"receipt_url":"https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUjltVWZSaGtmTU1vZTd0KJXnwMIGMgYwNqNG2bE6LBZcJSytKK_UPlN4WitJAmXiHM5AocmeSR8Sg9jdQha0bHwRbm5pQ1bMP4Eu","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}}} 
[2025-06-16 15:09:11] local.INFO: Payment intent created {"payment_intent":{"Stripe\\PaymentIntent":{"id":"pi_3RaeemRhkfMMoe7t0nrqhFpI","object":"payment_intent","amount":54839,"amount_capturable":0,"amount_details":{"tip":[]},"amount_received":0,"application":null,"application_fee_amount":null,"automatic_payment_methods":null,"canceled_at":null,"cancellation_reason":null,"capture_method":"automatic_async","client_secret":"pi_3RaeemRhkfMMoe7t0nrqhFpI_secret_9JjjNjNLpQI7SQqrbsikO3EPp","confirmation_method":"automatic","created":1750086548,"currency":"eur","customer":null,"description":null,"last_payment_error":null,"latest_charge":null,"livemode":false,"metadata":[],"next_action":null,"on_behalf_of":null,"payment_method":null,"payment_method_configuration_details":null,"payment_method_options":{"card":{"installments":null,"mandate_options":null,"network":null,"request_three_d_secure":"automatic"}},"payment_method_types":["card"],"presentment_details":{"presentment_amount":5672229,"presentment_currency":"inr"},"processing":null,"receipt_email":null,"review":null,"setup_future_usage":null,"shipping":null,"source":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"requires_payment_method","transfer_data":null,"transfer_group":null}}} 
[2025-06-16 15:09:12] local.INFO: Checkout session completed {"session_completed":{"Stripe\\Checkout\\Session":{"id":"cs_test_b1S30NmYg9lJXhus1B6oxSFiToIqQcQ0cGSYf7sOvCjtgUsNlZELRm8QQO","object":"checkout.session","adaptive_pricing":{"enabled":true},"after_expiration":null,"allow_promotion_codes":null,"amount_subtotal":54839,"amount_total":54839,"automatic_tax":{"enabled":false,"liability":null,"provider":null,"status":null},"billing_address_collection":null,"cancel_url":"http://ticketgol.test/checkout/cancel?session_id={CHECKOUT_SESSION_ID}","client_reference_id":null,"client_secret":null,"collected_information":{"shipping_details":null},"consent":null,"consent_collection":null,"created":**********,"currency":"eur","currency_conversion":null,"custom_fields":[],"custom_text":{"after_submit":null,"shipping_address":null,"submit":{"message":"We will send you an email with your tickets also can view your tickets in your profile."},"terms_of_service_acceptance":null},"customer":"cus_SVg0s9fFjkckT4","customer_creation":"if_required","customer_details":{"address":{"city":null,"country":"IN","line1":null,"line2":null,"postal_code":null,"state":null},"email":"<EMAIL>","name":"asdfg","phone":null,"tax_exempt":"none","tax_ids":[]},"customer_email":"<EMAIL>","discounts":[],"expires_at":**********,"invoice":"in_1RaeenRhkfMMoe7tp5EKsUJD","invoice_creation":{"enabled":true,"invoice_data":{"account_tax_ids":null,"custom_fields":null,"description":null,"footer":null,"issuer":{"type":"self"},"metadata":[],"rendering_options":null}},"livemode":false,"locale":null,"metadata":{"user_id":"7","order_id":"12","temp_ticket_reservation_id":"2"},"mode":"payment","payment_intent":"pi_3RaeemRhkfMMoe7t0nrqhFpI","payment_link":null,"payment_method_collection":"if_required","payment_method_configuration_details":null,"payment_method_options":{"card":{"request_three_d_secure":"automatic"}},"payment_method_types":["card"],"payment_status":"paid","permissions":null,"phone_number_collection":{"enabled":false},"presentment_details":{"presentment_amount":5672229,"presentment_currency":"inr"},"recovered_from":null,"saved_payment_method_options":{"allow_redisplay_filters":["always"],"payment_method_remove":"disabled","payment_method_save":null},"setup_intent":null,"shipping_address_collection":null,"shipping_cost":null,"shipping_options":[],"status":"complete","submit_type":null,"subscription":null,"success_url":"http://ticketgol.test/checkout/success?session_id={CHECKOUT_SESSION_ID}","total_details":{"amount_discount":0,"amount_shipping":0,"amount_tax":0},"ui_mode":"hosted","url":null,"wallet_options":null}}} 
[2025-06-16 15:09:12] local.INFO: webhook time {"time":"2025-06-16 15:09:12"} 
[2025-06-16 15:09:12] local.INFO: Transaction status updated to completed  
[2025-06-16 15:09:12] local.INFO: Order status updated to completed  
[2025-06-16 15:09:12] local.INFO: Reservation status updated to completed {"ticket_reservation":{"App\\Models\\TicketReservation":{"id":2,"ticket_id":8,"user_id":7,"quantity":"1","status":"completed","expires_at":"2025-06-16T15:20:52.000000Z","created_at":"2025-06-16T15:05:52.000000Z","updated_at":"2025-06-16T15:09:12.000000Z"}}} 
[2025-06-16 15:09:12] local.INFO: reservedCounter is  {"coutner":{"Redis":[]}} 
[2025-06-16 15:10:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":1,"ticket_id":6,"user_id":7,"quantity":"1","status":"active","expires_at":"2025-06-16T15:04:58.000000Z","created_at":"2025-06-16T14:49:58.000000Z","updated_at":"2025-06-16T14:49:58.000000Z"}]}} 
[2025-06-16 15:10:01] local.INFO: Updating expired records {"ids":[1]} 
[2025-06-16 15:10:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":1,"ticket_id":6,"user_id":7,"quantity":"1","status":"active","expires_at":"2025-06-16T15:04:58.000000Z","created_at":"2025-06-16T14:49:58.000000Z","updated_at":"2025-06-16T14:49:58.000000Z"}]}} 
[2025-06-16 15:10:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:10:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:10:02] local.INFO: Cron job executed {"time":"2025-06-16 15:10:02"} 
[2025-06-16 15:11:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:11:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:11:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:11:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:11:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:11:01] local.INFO: Cron job executed {"time":"2025-06-16 15:11:01"} 
[2025-06-16 15:12:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:12:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:12:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:12:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:12:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:12:01] local.INFO: Cron job executed {"time":"2025-06-16 15:12:01"} 
[2025-06-16 15:13:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:13:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:13:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:13:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:13:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":1,"status":"pending","order_id":11,"ticket_reservation_id":1,"user_id":7,"session_id":"cs_test_b14YcpktyFSKqJQ9CxBAlPj2NOGchfuGcSQ9hhQzG17xgXfnV6ncBA5oLw","currency_code":"eur","payment_intent_id":null,"payment_method_id":null,"payment_method_type":null,"total_amount":null,"paid_at":null,"refunded_at":null,"card_brand":null,"card_last_four":null,"created_at":"2025-06-16T14:52:50.000000Z","updated_at":"2025-06-16T14:52:50.000000Z","order":{"id":11,"order_no":"TGO011","buyer_id":7,"ticket_id":6,"quantity":"1","total_price":"42.43","status":"pending","purchase_date":"2025-06-16","description":null,"created_by":7,"created_at":"2025-06-16T14:52:42.000000Z","updated_at":"2025-06-16T14:52:42.000000Z","deleted_at":null}}]}} 
[2025-06-16 15:13:02] local.INFO: Transaction & Order status updated to expired  
[2025-06-16 15:13:02] local.INFO: Cron job executed {"time":"2025-06-16 15:13:02"} 
[2025-06-16 15:14:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:14:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:14:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:14:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:14:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:14:01] local.INFO: Cron job executed {"time":"2025-06-16 15:14:01"} 
[2025-06-16 15:15:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:15:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:15:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:15:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:15:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:15:01] local.INFO: Cron job executed {"time":"2025-06-16 15:15:01"} 
[2025-06-16 15:16:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:16:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:16:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:16:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:16:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:16:01] local.INFO: Cron job executed {"time":"2025-06-16 15:16:01"} 
[2025-06-16 15:17:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:17:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:17:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:17:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:17:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:17:01] local.INFO: Cron job executed {"time":"2025-06-16 15:17:01"} 
[2025-06-16 15:18:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:18:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:18:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:18:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:18:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:18:01] local.INFO: Cron job executed {"time":"2025-06-16 15:18:01"} 
[2025-06-16 15:19:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:19:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:19:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:19:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:19:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:19:01] local.INFO: Cron job executed {"time":"2025-06-16 15:19:01"} 
[2025-06-16 15:20:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:20:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:20:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:20:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:20:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:20:01] local.INFO: Cron job executed {"time":"2025-06-16 15:20:01"} 
[2025-06-16 15:21:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:21:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:21:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:21:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:21:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:21:02] local.INFO: Cron job executed {"time":"2025-06-16 15:21:02"} 
[2025-06-16 15:22:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:22:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:22:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:22:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:22:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:22:01] local.INFO: Cron job executed {"time":"2025-06-16 15:22:01"} 
[2025-06-16 15:23:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:23:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:23:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:23:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:23:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:23:01] local.INFO: Cron job executed {"time":"2025-06-16 15:23:01"} 
[2025-06-16 15:24:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:24:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:24:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:24:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:24:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:24:02] local.INFO: Cron job executed {"time":"2025-06-16 15:24:02"} 
[2025-06-16 15:25:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:25:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:25:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:25:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:25:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:25:01] local.INFO: Cron job executed {"time":"2025-06-16 15:25:01"} 
[2025-06-16 15:26:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:26:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:26:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:26:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:26:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:26:01] local.INFO: Cron job executed {"time":"2025-06-16 15:26:01"} 
[2025-06-16 15:27:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:27:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:27:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:27:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:27:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:27:01] local.INFO: Cron job executed {"time":"2025-06-16 15:27:01"} 
[2025-06-16 15:28:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:28:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:28:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:28:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:28:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:28:01] local.INFO: Cron job executed {"time":"2025-06-16 15:28:01"} 
[2025-06-16 15:29:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:29:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:29:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:29:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:29:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:29:01] local.INFO: Cron job executed {"time":"2025-06-16 15:29:01"} 
[2025-06-16 15:30:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:30:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:30:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:30:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:30:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:30:01] local.INFO: Cron job executed {"time":"2025-06-16 15:30:01"} 
[2025-06-16 15:31:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:31:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:31:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:31:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:31:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:31:01] local.INFO: Cron job executed {"time":"2025-06-16 15:31:01"} 
[2025-06-16 15:32:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:32:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:32:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:32:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:32:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:32:01] local.INFO: Cron job executed {"time":"2025-06-16 15:32:01"} 
[2025-06-16 15:33:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:33:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:33:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:33:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:33:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:33:01] local.INFO: Cron job executed {"time":"2025-06-16 15:33:01"} 
[2025-06-16 15:34:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:34:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:34:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:34:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:34:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:34:01] local.INFO: Cron job executed {"time":"2025-06-16 15:34:01"} 
[2025-06-16 15:35:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:35:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:35:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:35:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:35:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:35:01] local.INFO: Cron job executed {"time":"2025-06-16 15:35:01"} 
[2025-06-16 15:36:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:36:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:36:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:36:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:36:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:36:01] local.INFO: Cron job executed {"time":"2025-06-16 15:36:01"} 
[2025-06-16 15:37:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:37:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:37:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:37:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:37:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:37:01] local.INFO: Cron job executed {"time":"2025-06-16 15:37:01"} 
[2025-06-16 15:38:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:38:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:38:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:38:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:38:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:38:01] local.INFO: Cron job executed {"time":"2025-06-16 15:38:01"} 
[2025-06-16 15:39:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:39:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:39:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:39:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:39:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:39:01] local.INFO: Cron job executed {"time":"2025-06-16 15:39:01"} 
[2025-06-16 15:40:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:40:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:40:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:40:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:40:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:40:01] local.INFO: Cron job executed {"time":"2025-06-16 15:40:01"} 
[2025-06-16 15:41:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:41:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:41:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:41:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:41:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:41:01] local.INFO: Cron job executed {"time":"2025-06-16 15:41:01"} 
[2025-06-16 15:42:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:42:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:42:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:42:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:42:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:42:01] local.INFO: Cron job executed {"time":"2025-06-16 15:42:01"} 
[2025-06-16 15:43:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:43:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:43:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:43:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:43:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:43:01] local.INFO: Cron job executed {"time":"2025-06-16 15:43:01"} 
[2025-06-16 15:44:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:44:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:44:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:44:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:44:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:44:01] local.INFO: Cron job executed {"time":"2025-06-16 15:44:01"} 
[2025-06-16 15:45:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:45:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:45:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:45:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:45:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:45:01] local.INFO: Cron job executed {"time":"2025-06-16 15:45:01"} 
[2025-06-16 15:46:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:46:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:46:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:46:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:46:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:46:01] local.INFO: Cron job executed {"time":"2025-06-16 15:46:01"} 
[2025-06-16 15:47:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:47:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:47:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:47:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:47:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:47:01] local.INFO: Cron job executed {"time":"2025-06-16 15:47:01"} 
[2025-06-16 15:48:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:48:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:48:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:48:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:48:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:48:01] local.INFO: Cron job executed {"time":"2025-06-16 15:48:01"} 
[2025-06-16 15:49:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:49:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:49:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:49:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:49:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:49:01] local.INFO: Cron job executed {"time":"2025-06-16 15:49:01"} 
[2025-06-16 15:50:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:50:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:50:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:50:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:50:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:50:01] local.INFO: Cron job executed {"time":"2025-06-16 15:50:01"} 
[2025-06-16 15:51:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:51:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:51:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:51:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:51:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:51:01] local.INFO: Cron job executed {"time":"2025-06-16 15:51:01"} 
[2025-06-16 15:52:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:52:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:52:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:52:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:52:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:52:01] local.INFO: Cron job executed {"time":"2025-06-16 15:52:01"} 
[2025-06-16 15:53:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:53:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:53:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:53:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:53:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:53:01] local.INFO: Cron job executed {"time":"2025-06-16 15:53:01"} 
[2025-06-16 15:54:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:54:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:54:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:54:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:54:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:54:01] local.INFO: Cron job executed {"time":"2025-06-16 15:54:01"} 
[2025-06-16 15:55:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:55:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:55:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:55:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:55:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:55:01] local.INFO: Cron job executed {"time":"2025-06-16 15:55:01"} 
[2025-06-16 15:56:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:56:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:56:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:56:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:56:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:56:01] local.INFO: Cron job executed {"time":"2025-06-16 15:56:01"} 
[2025-06-16 15:57:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:57:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:57:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:57:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:57:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:57:01] local.INFO: Cron job executed {"time":"2025-06-16 15:57:01"} 
[2025-06-16 15:58:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:58:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:58:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:58:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:58:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:58:01] local.INFO: Cron job executed {"time":"2025-06-16 15:58:01"} 
[2025-06-16 15:59:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:59:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 15:59:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:59:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 15:59:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 15:59:01] local.INFO: Cron job executed {"time":"2025-06-16 15:59:01"} 
[2025-06-16 16:00:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:00:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:00:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:00:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:00:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:00:01] local.INFO: Cron job executed {"time":"2025-06-16 16:00:01"} 
[2025-06-16 16:01:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:01:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:01:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:01:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:01:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:01:01] local.INFO: Cron job executed {"time":"2025-06-16 16:01:01"} 
[2025-06-16 16:02:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:02:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:02:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:02:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:02:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:02:01] local.INFO: Cron job executed {"time":"2025-06-16 16:02:01"} 
[2025-06-16 16:03:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:03:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:03:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:03:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:03:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:03:01] local.INFO: Cron job executed {"time":"2025-06-16 16:03:01"} 
[2025-06-16 16:04:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:04:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:04:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:04:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:04:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:04:01] local.INFO: Cron job executed {"time":"2025-06-16 16:04:01"} 
[2025-06-16 16:05:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:05:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:05:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:05:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:05:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:05:01] local.INFO: Cron job executed {"time":"2025-06-16 16:05:01"} 
[2025-06-16 16:06:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:06:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:06:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:06:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:06:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:06:01] local.INFO: Cron job executed {"time":"2025-06-16 16:06:01"} 
[2025-06-16 16:07:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:07:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:07:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:07:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:07:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:07:01] local.INFO: Cron job executed {"time":"2025-06-16 16:07:01"} 
[2025-06-16 16:08:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:08:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:08:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:08:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:08:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:08:01] local.INFO: Cron job executed {"time":"2025-06-16 16:08:01"} 
[2025-06-16 16:09:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:09:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:09:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:09:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:09:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:09:01] local.INFO: Cron job executed {"time":"2025-06-16 16:09:01"} 
[2025-06-16 16:10:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:10:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:10:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:10:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:10:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:10:02] local.INFO: Cron job executed {"time":"2025-06-16 16:10:02"} 
[2025-06-16 16:11:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:11:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:11:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:11:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:11:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:11:01] local.INFO: Cron job executed {"time":"2025-06-16 16:11:01"} 
[2025-06-16 16:12:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:12:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:12:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:12:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:12:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:12:01] local.INFO: Cron job executed {"time":"2025-06-16 16:12:01"} 
[2025-06-16 16:13:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:13:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:13:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:13:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:13:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:13:01] local.INFO: Cron job executed {"time":"2025-06-16 16:13:01"} 
[2025-06-16 16:14:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:14:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:14:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:14:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:14:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:14:01] local.INFO: Cron job executed {"time":"2025-06-16 16:14:01"} 
[2025-06-16 16:15:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:15:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:15:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:15:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:15:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:15:01] local.INFO: Cron job executed {"time":"2025-06-16 16:15:01"} 
[2025-06-16 16:16:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:16:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:16:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:16:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:16:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:16:01] local.INFO: Cron job executed {"time":"2025-06-16 16:16:01"} 
[2025-06-16 16:17:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:17:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:17:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:17:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:17:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:17:01] local.INFO: Cron job executed {"time":"2025-06-16 16:17:01"} 
[2025-06-16 16:18:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:18:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:18:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:18:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:18:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:18:01] local.INFO: Cron job executed {"time":"2025-06-16 16:18:01"} 
[2025-06-16 16:19:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:19:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:19:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:19:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:19:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:19:01] local.INFO: Cron job executed {"time":"2025-06-16 16:19:01"} 
[2025-06-16 16:20:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:20:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:20:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:20:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:20:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:20:01] local.INFO: Cron job executed {"time":"2025-06-16 16:20:01"} 
[2025-06-16 16:21:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:21:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:21:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:21:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:21:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:21:01] local.INFO: Cron job executed {"time":"2025-06-16 16:21:01"} 
[2025-06-16 16:22:43] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:22:43] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:22:43] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:22:43] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:22:43] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:22:43] local.INFO: Cron job executed {"time":"2025-06-16 16:22:43"} 
[2025-06-16 16:23:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:23:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:23:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:23:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:23:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:23:02] local.INFO: Cron job executed {"time":"2025-06-16 16:23:02"} 
[2025-06-16 16:24:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:24:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:24:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:24:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:24:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:24:01] local.INFO: Cron job executed {"time":"2025-06-16 16:24:01"} 
[2025-06-16 16:25:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:25:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:25:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:25:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:25:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:25:01] local.INFO: Cron job executed {"time":"2025-06-16 16:25:01"} 
[2025-06-16 16:26:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:26:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:26:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:26:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:26:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:26:01] local.INFO: Cron job executed {"time":"2025-06-16 16:26:01"} 
[2025-06-16 16:27:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:27:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:27:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:27:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:27:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:27:01] local.INFO: Cron job executed {"time":"2025-06-16 16:27:01"} 
[2025-06-16 16:28:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:28:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:28:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:28:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:28:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:28:01] local.INFO: Cron job executed {"time":"2025-06-16 16:28:01"} 
[2025-06-16 16:29:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:29:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:29:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:29:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:29:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:29:01] local.INFO: Cron job executed {"time":"2025-06-16 16:29:01"} 
[2025-06-16 16:30:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:30:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:30:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:30:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:30:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:30:01] local.INFO: Cron job executed {"time":"2025-06-16 16:30:01"} 
[2025-06-16 16:31:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:31:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:31:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:31:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:31:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:31:01] local.INFO: Cron job executed {"time":"2025-06-16 16:31:01"} 
[2025-06-16 16:32:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:32:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:32:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:32:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:32:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:32:01] local.INFO: Cron job executed {"time":"2025-06-16 16:32:01"} 
[2025-06-16 16:33:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:33:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:33:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:33:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:33:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:33:01] local.INFO: Cron job executed {"time":"2025-06-16 16:33:01"} 
[2025-06-16 16:34:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:34:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:34:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:34:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:34:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:34:01] local.INFO: Cron job executed {"time":"2025-06-16 16:34:01"} 
[2025-06-16 16:35:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:35:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:35:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:35:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:35:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:35:01] local.INFO: Cron job executed {"time":"2025-06-16 16:35:01"} 
[2025-06-16 16:36:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:36:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:36:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:36:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:36:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:36:01] local.INFO: Cron job executed {"time":"2025-06-16 16:36:01"} 
[2025-06-16 16:37:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:37:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:37:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:37:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:37:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:37:01] local.INFO: Cron job executed {"time":"2025-06-16 16:37:01"} 
[2025-06-16 16:38:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:38:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:38:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:38:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:38:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:38:01] local.INFO: Cron job executed {"time":"2025-06-16 16:38:01"} 
[2025-06-16 16:39:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:39:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:39:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:39:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:39:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:39:01] local.INFO: Cron job executed {"time":"2025-06-16 16:39:01"} 
[2025-06-16 16:40:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:40:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:40:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:40:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:40:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:40:01] local.INFO: Cron job executed {"time":"2025-06-16 16:40:01"} 
[2025-06-16 16:41:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:41:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:41:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:41:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:41:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:41:01] local.INFO: Cron job executed {"time":"2025-06-16 16:41:01"} 
[2025-06-16 16:42:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:42:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:42:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:42:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:42:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:42:01] local.INFO: Cron job executed {"time":"2025-06-16 16:42:01"} 
[2025-06-16 16:43:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:43:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:43:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:43:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:43:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:43:01] local.INFO: Cron job executed {"time":"2025-06-16 16:43:01"} 
[2025-06-16 16:44:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:44:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:44:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:44:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:44:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:44:02] local.INFO: Cron job executed {"time":"2025-06-16 16:44:02"} 
[2025-06-16 16:45:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:45:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:45:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:45:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:45:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:45:01] local.INFO: Cron job executed {"time":"2025-06-16 16:45:01"} 
[2025-06-16 16:46:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:46:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:46:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:46:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:46:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:46:01] local.INFO: Cron job executed {"time":"2025-06-16 16:46:01"} 
[2025-06-16 16:47:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:47:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:47:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:47:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:47:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:47:01] local.INFO: Cron job executed {"time":"2025-06-16 16:47:01"} 
[2025-06-16 16:48:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:48:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:48:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:48:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:48:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:48:01] local.INFO: Cron job executed {"time":"2025-06-16 16:48:01"} 
[2025-06-16 16:49:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:49:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:49:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:49:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:49:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:49:01] local.INFO: Cron job executed {"time":"2025-06-16 16:49:01"} 
[2025-06-16 16:50:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:50:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:50:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:50:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:50:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:50:01] local.INFO: Cron job executed {"time":"2025-06-16 16:50:01"} 
[2025-06-16 16:51:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:51:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:51:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:51:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:51:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:51:01] local.INFO: Cron job executed {"time":"2025-06-16 16:51:01"} 
[2025-06-16 16:52:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:52:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:52:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:52:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:52:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:52:01] local.INFO: Cron job executed {"time":"2025-06-16 16:52:01"} 
[2025-06-16 16:53:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:53:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:53:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:53:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:53:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:53:01] local.INFO: Cron job executed {"time":"2025-06-16 16:53:01"} 
[2025-06-16 16:54:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:54:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:54:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:54:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:54:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:54:01] local.INFO: Cron job executed {"time":"2025-06-16 16:54:01"} 
[2025-06-16 16:55:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:55:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:55:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:55:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:55:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:55:01] local.INFO: Cron job executed {"time":"2025-06-16 16:55:01"} 
[2025-06-16 16:56:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:56:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:56:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:56:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:56:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:56:01] local.INFO: Cron job executed {"time":"2025-06-16 16:56:01"} 
[2025-06-16 16:57:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:57:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:57:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:57:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:57:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:57:01] local.INFO: Cron job executed {"time":"2025-06-16 16:57:01"} 
[2025-06-16 16:58:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:58:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:58:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:58:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:58:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:58:01] local.INFO: Cron job executed {"time":"2025-06-16 16:58:01"} 
[2025-06-16 16:59:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:59:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 16:59:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:59:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 16:59:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 16:59:02] local.INFO: Cron job executed {"time":"2025-06-16 16:59:02"} 
[2025-06-16 17:00:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:00:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:00:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:00:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:00:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:00:01] local.INFO: Cron job executed {"time":"2025-06-16 17:00:01"} 
[2025-06-16 17:01:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:01:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:01:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:01:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:01:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:01:01] local.INFO: Cron job executed {"time":"2025-06-16 17:01:01"} 
[2025-06-16 17:02:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:02:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:02:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:02:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:02:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:02:01] local.INFO: Cron job executed {"time":"2025-06-16 17:02:01"} 
[2025-06-16 17:03:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:03:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:03:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:03:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:03:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:03:01] local.INFO: Cron job executed {"time":"2025-06-16 17:03:01"} 
[2025-06-16 17:04:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:04:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:04:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:04:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:04:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:04:01] local.INFO: Cron job executed {"time":"2025-06-16 17:04:01"} 
[2025-06-16 17:05:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:05:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:05:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:05:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:05:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:05:01] local.INFO: Cron job executed {"time":"2025-06-16 17:05:01"} 
[2025-06-16 17:06:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:06:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:06:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:06:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:06:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:06:01] local.INFO: Cron job executed {"time":"2025-06-16 17:06:01"} 
[2025-06-16 17:07:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:07:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:07:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:07:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:07:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:07:01] local.INFO: Cron job executed {"time":"2025-06-16 17:07:01"} 
[2025-06-16 17:08:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:08:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:08:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:08:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:08:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:08:01] local.INFO: Cron job executed {"time":"2025-06-16 17:08:01"} 
[2025-06-16 17:09:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:09:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:09:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:09:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:09:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:09:01] local.INFO: Cron job executed {"time":"2025-06-16 17:09:01"} 
[2025-06-16 17:10:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:10:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:10:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:10:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:10:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:10:01] local.INFO: Cron job executed {"time":"2025-06-16 17:10:01"} 
[2025-06-16 17:11:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:11:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:11:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:11:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:11:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:11:01] local.INFO: Cron job executed {"time":"2025-06-16 17:11:01"} 
[2025-06-16 17:12:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:12:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:12:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:12:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:12:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:12:01] local.INFO: Cron job executed {"time":"2025-06-16 17:12:01"} 
[2025-06-16 17:13:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:13:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:13:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:13:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:13:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:13:01] local.INFO: Cron job executed {"time":"2025-06-16 17:13:01"} 
[2025-06-16 17:14:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:14:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:14:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:14:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:14:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:14:01] local.INFO: Cron job executed {"time":"2025-06-16 17:14:01"} 
[2025-06-16 17:15:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:15:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:15:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:15:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:15:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:15:01] local.INFO: Cron job executed {"time":"2025-06-16 17:15:01"} 
[2025-06-16 17:16:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:16:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:16:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:16:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:16:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:16:01] local.INFO: Cron job executed {"time":"2025-06-16 17:16:01"} 
[2025-06-16 17:17:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:17:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:17:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:17:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:17:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:17:01] local.INFO: Cron job executed {"time":"2025-06-16 17:17:01"} 
[2025-06-16 17:18:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:18:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:18:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:18:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:18:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:18:01] local.INFO: Cron job executed {"time":"2025-06-16 17:18:01"} 
[2025-06-16 17:19:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:19:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:19:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:19:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:19:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:19:01] local.INFO: Cron job executed {"time":"2025-06-16 17:19:01"} 
[2025-06-16 17:20:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:20:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:20:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:20:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:20:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:20:01] local.INFO: Cron job executed {"time":"2025-06-16 17:20:01"} 
[2025-06-16 17:21:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:21:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:21:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:21:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:21:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:21:01] local.INFO: Cron job executed {"time":"2025-06-16 17:21:01"} 
[2025-06-16 17:22:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:22:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:22:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:22:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:22:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:22:01] local.INFO: Cron job executed {"time":"2025-06-16 17:22:01"} 
[2025-06-16 17:23:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:23:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:23:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:23:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:23:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:23:01] local.INFO: Cron job executed {"time":"2025-06-16 17:23:01"} 
[2025-06-16 17:24:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:24:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:24:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:24:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:24:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:24:01] local.INFO: Cron job executed {"time":"2025-06-16 17:24:01"} 
[2025-06-16 17:25:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:25:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:25:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:25:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:25:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:25:01] local.INFO: Cron job executed {"time":"2025-06-16 17:25:01"} 
[2025-06-16 17:26:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:26:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:26:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:26:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:26:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:26:01] local.INFO: Cron job executed {"time":"2025-06-16 17:26:01"} 
[2025-06-16 17:27:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:27:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:27:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:27:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:27:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:27:01] local.INFO: Cron job executed {"time":"2025-06-16 17:27:01"} 
[2025-06-16 17:28:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:28:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:28:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:28:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:28:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:28:01] local.INFO: Cron job executed {"time":"2025-06-16 17:28:01"} 
[2025-06-16 17:29:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:29:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:29:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:29:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:29:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:29:01] local.INFO: Cron job executed {"time":"2025-06-16 17:29:01"} 
[2025-06-16 17:30:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:30:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:30:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:30:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:30:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:30:01] local.INFO: Cron job executed {"time":"2025-06-16 17:30:01"} 
[2025-06-16 17:31:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:31:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:31:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:31:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:31:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:31:01] local.INFO: Cron job executed {"time":"2025-06-16 17:31:01"} 
[2025-06-16 17:32:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:32:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:32:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:32:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:32:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:32:01] local.INFO: Cron job executed {"time":"2025-06-16 17:32:01"} 
[2025-06-16 17:33:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:33:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:33:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:33:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:33:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:33:01] local.INFO: Cron job executed {"time":"2025-06-16 17:33:01"} 
[2025-06-16 17:34:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:34:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:34:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:34:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:34:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:34:01] local.INFO: Cron job executed {"time":"2025-06-16 17:34:01"} 
[2025-06-16 17:35:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:35:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:35:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:35:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:35:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:35:01] local.INFO: Cron job executed {"time":"2025-06-16 17:35:01"} 
[2025-06-16 17:36:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:36:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:36:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:36:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:36:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:36:02] local.INFO: Cron job executed {"time":"2025-06-16 17:36:02"} 
[2025-06-16 17:37:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:37:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:37:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:37:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:37:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:37:02] local.INFO: Cron job executed {"time":"2025-06-16 17:37:02"} 
[2025-06-16 17:38:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:38:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:38:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:38:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:38:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:38:01] local.INFO: Cron job executed {"time":"2025-06-16 17:38:01"} 
[2025-06-16 17:39:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:39:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:39:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:39:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:39:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:39:01] local.INFO: Cron job executed {"time":"2025-06-16 17:39:01"} 
[2025-06-16 17:40:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:40:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:40:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:40:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:40:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:40:01] local.INFO: Cron job executed {"time":"2025-06-16 17:40:01"} 
[2025-06-16 17:41:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:41:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:41:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:41:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:41:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:41:01] local.INFO: Cron job executed {"time":"2025-06-16 17:41:01"} 
[2025-06-16 17:42:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:42:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:42:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:42:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:42:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:42:01] local.INFO: Cron job executed {"time":"2025-06-16 17:42:01"} 
[2025-06-16 17:43:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:43:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:43:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:43:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:43:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:43:01] local.INFO: Cron job executed {"time":"2025-06-16 17:43:01"} 
[2025-06-16 17:44:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:44:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:44:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:44:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:44:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:44:02] local.INFO: Cron job executed {"time":"2025-06-16 17:44:02"} 
[2025-06-16 17:45:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:45:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:45:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:45:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:45:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:45:01] local.INFO: Cron job executed {"time":"2025-06-16 17:45:01"} 
[2025-06-16 17:46:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:46:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:46:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:46:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:46:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:46:01] local.INFO: Cron job executed {"time":"2025-06-16 17:46:01"} 
[2025-06-16 17:47:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:47:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:47:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:47:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:47:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:47:01] local.INFO: Cron job executed {"time":"2025-06-16 17:47:01"} 
[2025-06-16 17:48:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:48:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:48:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:48:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:48:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:48:01] local.INFO: Cron job executed {"time":"2025-06-16 17:48:01"} 
[2025-06-16 17:49:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:49:03] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:49:03] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:49:03] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:49:03] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:49:03] local.INFO: Cron job executed {"time":"2025-06-16 17:49:03"} 
[2025-06-16 17:50:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:50:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:50:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:50:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:50:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:50:01] local.INFO: Cron job executed {"time":"2025-06-16 17:50:01"} 
[2025-06-16 17:51:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:51:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:51:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:51:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:51:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:51:01] local.INFO: Cron job executed {"time":"2025-06-16 17:51:01"} 
[2025-06-16 17:52:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:52:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:52:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:52:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:52:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:52:01] local.INFO: Cron job executed {"time":"2025-06-16 17:52:01"} 
[2025-06-16 17:53:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:53:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:53:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:53:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:53:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:53:01] local.INFO: Cron job executed {"time":"2025-06-16 17:53:01"} 
[2025-06-16 17:54:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:54:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:54:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:54:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:54:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:54:02] local.INFO: Cron job executed {"time":"2025-06-16 17:54:02"} 
[2025-06-16 17:55:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:55:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:55:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:55:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:55:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:55:01] local.INFO: Cron job executed {"time":"2025-06-16 17:55:01"} 
[2025-06-16 17:56:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:56:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:56:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:56:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:56:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:56:02] local.INFO: Cron job executed {"time":"2025-06-16 17:56:02"} 
[2025-06-16 17:57:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:57:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:57:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:57:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:57:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:57:01] local.INFO: Cron job executed {"time":"2025-06-16 17:57:01"} 
[2025-06-16 17:58:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:58:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:58:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:58:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:58:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:58:01] local.INFO: Cron job executed {"time":"2025-06-16 17:58:01"} 
[2025-06-16 17:59:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:59:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 17:59:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:59:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 17:59:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 17:59:01] local.INFO: Cron job executed {"time":"2025-06-16 17:59:01"} 
[2025-06-16 18:00:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:00:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:00:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:00:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:00:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:00:01] local.INFO: Cron job executed {"time":"2025-06-16 18:00:01"} 
[2025-06-16 18:01:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:01:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:01:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:01:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:01:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:01:01] local.INFO: Cron job executed {"time":"2025-06-16 18:01:01"} 
[2025-06-16 18:02:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:02:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:02:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:02:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:02:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:02:02] local.INFO: Cron job executed {"time":"2025-06-16 18:02:02"} 
[2025-06-16 18:03:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:03:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:03:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:03:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:03:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:03:01] local.INFO: Cron job executed {"time":"2025-06-16 18:03:01"} 
[2025-06-16 18:04:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:04:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:04:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:04:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:04:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:04:01] local.INFO: Cron job executed {"time":"2025-06-16 18:04:01"} 
[2025-06-16 18:05:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:05:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:05:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:05:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:05:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:05:01] local.INFO: Cron job executed {"time":"2025-06-16 18:05:01"} 
[2025-06-16 18:06:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:06:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:06:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:06:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:06:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:06:02] local.INFO: Cron job executed {"time":"2025-06-16 18:06:02"} 
[2025-06-16 18:07:03] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:07:03] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:07:03] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:07:03] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:07:03] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:07:03] local.INFO: Cron job executed {"time":"2025-06-16 18:07:03"} 
[2025-06-16 18:08:03] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:08:03] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:08:03] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:08:03] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:08:03] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:08:03] local.INFO: Cron job executed {"time":"2025-06-16 18:08:03"} 
[2025-06-16 18:09:03] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:09:03] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:09:03] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:09:03] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:09:03] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:09:03] local.INFO: Cron job executed {"time":"2025-06-16 18:09:03"} 
[2025-06-16 18:10:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:10:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:10:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:10:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:10:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:10:02] local.INFO: Cron job executed {"time":"2025-06-16 18:10:02"} 
[2025-06-16 18:11:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:11:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:11:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:11:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:11:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:11:02] local.INFO: Cron job executed {"time":"2025-06-16 18:11:02"} 
[2025-06-16 18:12:03] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:12:03] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:12:03] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:12:03] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:12:03] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:12:03] local.INFO: Cron job executed {"time":"2025-06-16 18:12:03"} 
[2025-06-16 18:13:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:13:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:13:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:13:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:13:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:13:01] local.INFO: Cron job executed {"time":"2025-06-16 18:13:01"} 
[2025-06-16 18:14:03] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:14:03] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:14:03] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:14:03] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:14:03] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:14:03] local.INFO: Cron job executed {"time":"2025-06-16 18:14:03"} 
[2025-06-16 18:15:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:15:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:15:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:15:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:15:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:15:01] local.INFO: Cron job executed {"time":"2025-06-16 18:15:01"} 
[2025-06-16 18:16:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:16:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:16:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:16:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:16:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:16:02] local.INFO: Cron job executed {"time":"2025-06-16 18:16:02"} 
[2025-06-16 18:17:03] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:17:03] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:17:03] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:17:03] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:17:03] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:17:03] local.INFO: Cron job executed {"time":"2025-06-16 18:17:03"} 
[2025-06-16 18:18:03] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:18:03] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:18:03] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:18:03] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:18:03] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:18:03] local.INFO: Cron job executed {"time":"2025-06-16 18:18:03"} 
[2025-06-16 18:19:03] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:19:03] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:19:03] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:19:03] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:19:03] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:19:03] local.INFO: Cron job executed {"time":"2025-06-16 18:19:03"} 
[2025-06-16 18:20:03] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:20:03] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:20:03] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:20:03] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:20:03] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:20:03] local.INFO: Cron job executed {"time":"2025-06-16 18:20:03"} 
[2025-06-16 18:21:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:21:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:21:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:21:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:21:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:21:02] local.INFO: Cron job executed {"time":"2025-06-16 18:21:02"} 
[2025-06-16 18:22:03] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:22:03] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:22:03] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:22:03] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:22:03] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:22:03] local.INFO: Cron job executed {"time":"2025-06-16 18:22:03"} 
[2025-06-16 18:23:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:23:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:23:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:23:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:23:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:23:01] local.INFO: Cron job executed {"time":"2025-06-16 18:23:01"} 
[2025-06-16 18:24:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:24:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:24:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:24:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:24:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:24:01] local.INFO: Cron job executed {"time":"2025-06-16 18:24:01"} 
[2025-06-16 18:25:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:25:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:25:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:25:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:25:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:25:02] local.INFO: Cron job executed {"time":"2025-06-16 18:25:02"} 
[2025-06-16 18:26:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:26:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:26:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:26:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:26:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:26:02] local.INFO: Cron job executed {"time":"2025-06-16 18:26:02"} 
[2025-06-16 18:27:03] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:27:03] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:27:03] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:27:03] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:27:03] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:27:03] local.INFO: Cron job executed {"time":"2025-06-16 18:27:03"} 
[2025-06-16 18:28:03] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:28:03] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:28:03] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:28:03] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:28:03] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:28:03] local.INFO: Cron job executed {"time":"2025-06-16 18:28:03"} 
[2025-06-16 18:29:03] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:29:03] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:29:03] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:29:03] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:29:03] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:29:03] local.INFO: Cron job executed {"time":"2025-06-16 18:29:03"} 
[2025-06-16 18:30:03] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:30:03] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:30:03] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:30:03] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:30:03] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:30:03] local.INFO: Cron job executed {"time":"2025-06-16 18:30:03"} 
[2025-06-16 18:31:04] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:31:04] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:31:04] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:31:04] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:31:04] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:31:04] local.INFO: Cron job executed {"time":"2025-06-16 18:31:04"} 
[2025-06-16 18:32:04] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:32:04] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:32:04] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:32:04] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:32:04] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:32:04] local.INFO: Cron job executed {"time":"2025-06-16 18:32:04"} 
[2025-06-16 18:33:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:33:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:33:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:33:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:33:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:33:02] local.INFO: Cron job executed {"time":"2025-06-16 18:33:02"} 
[2025-06-16 18:34:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:34:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:34:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:34:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:34:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:34:02] local.INFO: Cron job executed {"time":"2025-06-16 18:34:02"} 
[2025-06-16 18:35:03] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:35:03] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:35:03] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:35:03] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:35:03] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:35:03] local.INFO: Cron job executed {"time":"2025-06-16 18:35:03"} 
[2025-06-16 18:36:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:36:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:36:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:36:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:36:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:36:01] local.INFO: Cron job executed {"time":"2025-06-16 18:36:01"} 
[2025-06-16 18:37:05] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:37:05] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:37:05] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:37:05] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:37:05] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:37:05] local.INFO: Cron job executed {"time":"2025-06-16 18:37:05"} 
[2025-06-16 18:38:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:38:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:38:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:38:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:38:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:38:01] local.INFO: Cron job executed {"time":"2025-06-16 18:38:01"} 
[2025-06-16 18:39:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:39:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:39:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:39:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:39:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:39:01] local.INFO: Cron job executed {"time":"2025-06-16 18:39:01"} 
[2025-06-16 18:40:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:40:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:40:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:40:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:40:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:40:01] local.INFO: Cron job executed {"time":"2025-06-16 18:40:01"} 
[2025-06-16 18:41:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:41:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:41:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:41:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:41:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:41:02] local.INFO: Cron job executed {"time":"2025-06-16 18:41:02"} 
[2025-06-16 18:42:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:42:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:42:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:42:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:42:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:42:02] local.INFO: Cron job executed {"time":"2025-06-16 18:42:02"} 
[2025-06-16 18:43:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:43:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:43:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:43:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:43:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:43:02] local.INFO: Cron job executed {"time":"2025-06-16 18:43:02"} 
[2025-06-16 18:44:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:44:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:44:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:44:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:44:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:44:02] local.INFO: Cron job executed {"time":"2025-06-16 18:44:02"} 
[2025-06-16 18:45:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:45:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:45:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:45:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:45:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:45:02] local.INFO: Cron job executed {"time":"2025-06-16 18:45:02"} 
[2025-06-16 18:46:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:46:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:46:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:46:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:46:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:46:01] local.INFO: Cron job executed {"time":"2025-06-16 18:46:01"} 
[2025-06-16 18:47:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:47:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:47:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:47:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:47:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:47:02] local.INFO: Cron job executed {"time":"2025-06-16 18:47:02"} 
[2025-06-16 18:48:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:48:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:48:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:48:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:48:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:48:02] local.INFO: Cron job executed {"time":"2025-06-16 18:48:02"} 
[2025-06-16 18:49:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:49:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:49:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:49:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:49:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:49:02] local.INFO: Cron job executed {"time":"2025-06-16 18:49:02"} 
[2025-06-16 18:50:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:50:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:50:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:50:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:50:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:50:01] local.INFO: Cron job executed {"time":"2025-06-16 18:50:01"} 
[2025-06-16 18:51:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:51:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:51:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:51:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:51:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:51:01] local.INFO: Cron job executed {"time":"2025-06-16 18:51:01"} 
[2025-06-16 18:52:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:52:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:52:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:52:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:52:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:52:01] local.INFO: Cron job executed {"time":"2025-06-16 18:52:01"} 
[2025-06-16 18:53:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:53:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:53:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:53:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:53:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:53:01] local.INFO: Cron job executed {"time":"2025-06-16 18:53:01"} 
[2025-06-16 18:54:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:54:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:54:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:54:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:54:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:54:01] local.INFO: Cron job executed {"time":"2025-06-16 18:54:01"} 
[2025-06-16 18:55:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:55:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:55:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:55:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:55:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:55:01] local.INFO: Cron job executed {"time":"2025-06-16 18:55:01"} 
[2025-06-16 18:56:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:56:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:56:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:56:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:56:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:56:01] local.INFO: Cron job executed {"time":"2025-06-16 18:56:01"} 
[2025-06-16 18:57:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:57:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:57:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:57:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:57:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:57:01] local.INFO: Cron job executed {"time":"2025-06-16 18:57:01"} 
[2025-06-16 18:58:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:58:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:58:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:58:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:58:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:58:01] local.INFO: Cron job executed {"time":"2025-06-16 18:58:01"} 
[2025-06-16 18:59:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:59:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 18:59:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:59:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 18:59:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 18:59:02] local.INFO: Cron job executed {"time":"2025-06-16 18:59:02"} 
[2025-06-16 19:00:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:00:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:00:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:00:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:00:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:00:01] local.INFO: Cron job executed {"time":"2025-06-16 19:00:01"} 
[2025-06-16 19:01:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:01:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:01:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:01:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:01:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:01:01] local.INFO: Cron job executed {"time":"2025-06-16 19:01:01"} 
[2025-06-16 19:02:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:02:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:02:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:02:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:02:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:02:01] local.INFO: Cron job executed {"time":"2025-06-16 19:02:01"} 
[2025-06-16 19:03:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:03:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:03:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:03:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:03:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:03:01] local.INFO: Cron job executed {"time":"2025-06-16 19:03:01"} 
[2025-06-16 19:04:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:04:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:04:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:04:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:04:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:04:01] local.INFO: Cron job executed {"time":"2025-06-16 19:04:01"} 
[2025-06-16 19:05:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:05:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:05:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:05:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:05:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:05:01] local.INFO: Cron job executed {"time":"2025-06-16 19:05:01"} 
[2025-06-16 19:06:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:06:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:06:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:06:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:06:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:06:01] local.INFO: Cron job executed {"time":"2025-06-16 19:06:01"} 
[2025-06-16 19:07:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:07:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:07:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:07:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:07:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:07:02] local.INFO: Cron job executed {"time":"2025-06-16 19:07:02"} 
[2025-06-16 19:08:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:08:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:08:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:08:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:08:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:08:01] local.INFO: Cron job executed {"time":"2025-06-16 19:08:01"} 
[2025-06-16 19:09:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:09:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:09:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:09:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:09:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:09:01] local.INFO: Cron job executed {"time":"2025-06-16 19:09:01"} 
[2025-06-16 19:10:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:10:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:10:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:10:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:10:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:10:01] local.INFO: Cron job executed {"time":"2025-06-16 19:10:01"} 
[2025-06-16 19:11:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:11:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:11:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:11:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:11:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:11:02] local.INFO: Cron job executed {"time":"2025-06-16 19:11:02"} 
[2025-06-16 19:12:04] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:12:04] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:12:04] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:12:04] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:12:04] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:12:04] local.INFO: Cron job executed {"time":"2025-06-16 19:12:04"} 
[2025-06-16 19:13:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:13:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:13:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:13:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:13:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:13:01] local.INFO: Cron job executed {"time":"2025-06-16 19:13:01"} 
[2025-06-16 19:14:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:14:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:14:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:14:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:14:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:14:01] local.INFO: Cron job executed {"time":"2025-06-16 19:14:01"} 
[2025-06-16 19:15:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:15:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:15:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:15:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:15:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:15:01] local.INFO: Cron job executed {"time":"2025-06-16 19:15:01"} 
[2025-06-16 19:16:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:16:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:16:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:16:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:16:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:16:01] local.INFO: Cron job executed {"time":"2025-06-16 19:16:01"} 
[2025-06-16 19:17:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:17:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:17:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:17:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:17:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:17:01] local.INFO: Cron job executed {"time":"2025-06-16 19:17:01"} 
[2025-06-16 19:18:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:18:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:18:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:18:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:18:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:18:01] local.INFO: Cron job executed {"time":"2025-06-16 19:18:01"} 
[2025-06-16 19:19:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:19:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:19:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:19:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:19:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:19:01] local.INFO: Cron job executed {"time":"2025-06-16 19:19:01"} 
[2025-06-16 19:20:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:20:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:20:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:20:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:20:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:20:02] local.INFO: Cron job executed {"time":"2025-06-16 19:20:02"} 
[2025-06-16 19:21:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:21:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:21:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:21:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:21:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:21:01] local.INFO: Cron job executed {"time":"2025-06-16 19:21:01"} 
[2025-06-16 19:22:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:22:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:22:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:22:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:22:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:22:01] local.INFO: Cron job executed {"time":"2025-06-16 19:22:01"} 
[2025-06-16 19:23:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:23:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:23:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:23:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:23:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:23:01] local.INFO: Cron job executed {"time":"2025-06-16 19:23:01"} 
[2025-06-16 19:24:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:24:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:24:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:24:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:24:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:24:01] local.INFO: Cron job executed {"time":"2025-06-16 19:24:01"} 
[2025-06-16 19:25:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:25:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:25:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:25:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:25:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:25:01] local.INFO: Cron job executed {"time":"2025-06-16 19:25:01"} 
[2025-06-16 19:26:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:26:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:26:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:26:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:26:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:26:01] local.INFO: Cron job executed {"time":"2025-06-16 19:26:01"} 
[2025-06-16 19:27:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:27:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:27:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:27:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:27:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:27:01] local.INFO: Cron job executed {"time":"2025-06-16 19:27:01"} 
[2025-06-16 19:28:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:28:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:28:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:28:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:28:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:28:01] local.INFO: Cron job executed {"time":"2025-06-16 19:28:01"} 
[2025-06-16 19:29:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:29:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:29:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:29:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:29:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:29:01] local.INFO: Cron job executed {"time":"2025-06-16 19:29:01"} 
[2025-06-16 19:30:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:30:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:30:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:30:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:30:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:30:01] local.INFO: Cron job executed {"time":"2025-06-16 19:30:01"} 
[2025-06-16 19:31:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:31:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:31:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:31:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:31:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:31:01] local.INFO: Cron job executed {"time":"2025-06-16 19:31:01"} 
[2025-06-16 19:32:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:32:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:32:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:32:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:32:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:32:01] local.INFO: Cron job executed {"time":"2025-06-16 19:32:01"} 
[2025-06-16 19:33:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:33:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:33:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:33:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:33:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:33:01] local.INFO: Cron job executed {"time":"2025-06-16 19:33:01"} 
[2025-06-16 19:34:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:34:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:34:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:34:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:34:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:34:01] local.INFO: Cron job executed {"time":"2025-06-16 19:34:01"} 
[2025-06-16 19:35:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:35:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:35:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:35:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:35:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:35:01] local.INFO: Cron job executed {"time":"2025-06-16 19:35:01"} 
[2025-06-16 19:36:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:36:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:36:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:36:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:36:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:36:01] local.INFO: Cron job executed {"time":"2025-06-16 19:36:01"} 
[2025-06-16 19:37:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:37:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:37:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:37:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:37:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:37:01] local.INFO: Cron job executed {"time":"2025-06-16 19:37:01"} 
[2025-06-16 19:38:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:38:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:38:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:38:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:38:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:38:01] local.INFO: Cron job executed {"time":"2025-06-16 19:38:01"} 
[2025-06-16 19:39:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:39:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:39:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:39:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:39:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:39:01] local.INFO: Cron job executed {"time":"2025-06-16 19:39:01"} 
[2025-06-16 19:40:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:40:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:40:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:40:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:40:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:40:01] local.INFO: Cron job executed {"time":"2025-06-16 19:40:01"} 
[2025-06-16 19:41:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:41:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:41:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:41:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:41:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:41:01] local.INFO: Cron job executed {"time":"2025-06-16 19:41:01"} 
[2025-06-16 19:42:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:42:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:42:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:42:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:42:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:42:01] local.INFO: Cron job executed {"time":"2025-06-16 19:42:01"} 
[2025-06-16 19:43:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:43:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:43:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:43:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:43:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:43:01] local.INFO: Cron job executed {"time":"2025-06-16 19:43:01"} 
[2025-06-16 19:44:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:44:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:44:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:44:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:44:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:44:01] local.INFO: Cron job executed {"time":"2025-06-16 19:44:01"} 
[2025-06-16 19:45:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:45:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:45:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:45:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:45:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:45:01] local.INFO: Cron job executed {"time":"2025-06-16 19:45:01"} 
[2025-06-16 19:46:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:46:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:46:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:46:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:46:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:46:01] local.INFO: Cron job executed {"time":"2025-06-16 19:46:01"} 
[2025-06-16 19:47:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:47:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:47:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:47:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:47:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:47:01] local.INFO: Cron job executed {"time":"2025-06-16 19:47:01"} 
[2025-06-16 19:48:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:48:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:48:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:48:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:48:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:48:01] local.INFO: Cron job executed {"time":"2025-06-16 19:48:01"} 
[2025-06-16 19:49:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:49:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:49:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:49:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:49:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:49:01] local.INFO: Cron job executed {"time":"2025-06-16 19:49:01"} 
[2025-06-16 19:50:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:50:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:50:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:50:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:50:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:50:01] local.INFO: Cron job executed {"time":"2025-06-16 19:50:01"} 
[2025-06-16 19:51:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:51:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:51:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:51:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:51:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:51:01] local.INFO: Cron job executed {"time":"2025-06-16 19:51:01"} 
[2025-06-16 19:52:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:52:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:52:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:52:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:52:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:52:01] local.INFO: Cron job executed {"time":"2025-06-16 19:52:01"} 
[2025-06-16 19:53:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:53:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:53:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:53:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:53:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:53:01] local.INFO: Cron job executed {"time":"2025-06-16 19:53:01"} 
[2025-06-16 19:54:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:54:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:54:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:54:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:54:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:54:01] local.INFO: Cron job executed {"time":"2025-06-16 19:54:01"} 
[2025-06-16 19:55:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:55:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:55:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:55:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:55:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:55:01] local.INFO: Cron job executed {"time":"2025-06-16 19:55:01"} 
[2025-06-16 19:56:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:56:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:56:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:56:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:56:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:56:01] local.INFO: Cron job executed {"time":"2025-06-16 19:56:01"} 
[2025-06-16 19:57:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:57:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:57:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:57:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:57:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:57:01] local.INFO: Cron job executed {"time":"2025-06-16 19:57:01"} 
[2025-06-16 19:58:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:58:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:58:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:58:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:58:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:58:01] local.INFO: Cron job executed {"time":"2025-06-16 19:58:01"} 
[2025-06-16 19:59:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:59:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 19:59:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:59:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 19:59:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 19:59:01] local.INFO: Cron job executed {"time":"2025-06-16 19:59:01"} 
[2025-06-16 20:00:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:00:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:00:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:00:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:00:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:00:01] local.INFO: Cron job executed {"time":"2025-06-16 20:00:01"} 
[2025-06-16 20:01:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:01:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:01:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:01:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:01:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:01:01] local.INFO: Cron job executed {"time":"2025-06-16 20:01:01"} 
[2025-06-16 20:02:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:02:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:02:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:02:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:02:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:02:01] local.INFO: Cron job executed {"time":"2025-06-16 20:02:01"} 
[2025-06-16 20:03:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:03:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:03:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:03:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:03:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:03:01] local.INFO: Cron job executed {"time":"2025-06-16 20:03:01"} 
[2025-06-16 20:04:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:04:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:04:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:04:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:04:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:04:01] local.INFO: Cron job executed {"time":"2025-06-16 20:04:01"} 
[2025-06-16 20:05:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:05:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:05:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:05:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:05:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:05:01] local.INFO: Cron job executed {"time":"2025-06-16 20:05:01"} 
[2025-06-16 20:06:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:06:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:06:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:06:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:06:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:06:01] local.INFO: Cron job executed {"time":"2025-06-16 20:06:01"} 
[2025-06-16 20:07:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:07:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:07:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:07:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:07:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:07:01] local.INFO: Cron job executed {"time":"2025-06-16 20:07:01"} 
[2025-06-16 20:08:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:08:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:08:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:08:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:08:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:08:01] local.INFO: Cron job executed {"time":"2025-06-16 20:08:01"} 
[2025-06-16 20:09:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:09:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:09:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:09:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:09:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:09:01] local.INFO: Cron job executed {"time":"2025-06-16 20:09:01"} 
[2025-06-16 20:10:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:10:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:10:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:10:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:10:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:10:01] local.INFO: Cron job executed {"time":"2025-06-16 20:10:01"} 
[2025-06-16 20:11:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:11:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:11:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:11:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:11:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:11:01] local.INFO: Cron job executed {"time":"2025-06-16 20:11:01"} 
[2025-06-16 20:12:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:12:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:12:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:12:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:12:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:12:01] local.INFO: Cron job executed {"time":"2025-06-16 20:12:01"} 
[2025-06-16 20:13:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:13:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:13:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:13:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:13:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:13:01] local.INFO: Cron job executed {"time":"2025-06-16 20:13:01"} 
[2025-06-16 20:14:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:14:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:14:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:14:02] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:14:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:14:02] local.INFO: Cron job executed {"time":"2025-06-16 20:14:02"} 
[2025-06-16 20:15:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:15:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:15:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:15:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:15:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:15:01] local.INFO: Cron job executed {"time":"2025-06-16 20:15:01"} 
[2025-06-16 20:16:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:16:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:16:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:16:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:16:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:16:01] local.INFO: Cron job executed {"time":"2025-06-16 20:16:01"} 
[2025-06-16 20:17:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:17:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:17:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:17:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:17:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:17:01] local.INFO: Cron job executed {"time":"2025-06-16 20:17:01"} 
[2025-06-16 20:18:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:18:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:18:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:18:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:18:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:18:01] local.INFO: Cron job executed {"time":"2025-06-16 20:18:01"} 
[2025-06-16 20:19:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:19:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:19:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:19:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:19:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:19:01] local.INFO: Cron job executed {"time":"2025-06-16 20:19:01"} 
[2025-06-16 20:20:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:20:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:20:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:20:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:20:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:20:01] local.INFO: Cron job executed {"time":"2025-06-16 20:20:01"} 
[2025-06-16 20:21:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:21:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:21:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:21:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:21:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:21:01] local.INFO: Cron job executed {"time":"2025-06-16 20:21:01"} 
[2025-06-16 20:22:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:22:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:22:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:22:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:22:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:22:01] local.INFO: Cron job executed {"time":"2025-06-16 20:22:01"} 
[2025-06-16 20:23:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:23:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:23:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:23:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:23:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:23:01] local.INFO: Cron job executed {"time":"2025-06-16 20:23:01"} 
[2025-06-16 20:24:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:24:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:24:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:24:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:24:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:24:01] local.INFO: Cron job executed {"time":"2025-06-16 20:24:01"} 
[2025-06-16 20:25:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:25:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:25:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:25:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:25:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:25:01] local.INFO: Cron job executed {"time":"2025-06-16 20:25:01"} 
[2025-06-16 20:26:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:26:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:26:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:26:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:26:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:26:01] local.INFO: Cron job executed {"time":"2025-06-16 20:26:01"} 
[2025-06-16 20:27:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:27:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:27:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:27:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:27:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:27:01] local.INFO: Cron job executed {"time":"2025-06-16 20:27:01"} 
[2025-06-16 20:28:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:28:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:28:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:28:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:28:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:28:01] local.INFO: Cron job executed {"time":"2025-06-16 20:28:01"} 
[2025-06-16 20:29:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:29:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:29:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:29:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:29:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:29:01] local.INFO: Cron job executed {"time":"2025-06-16 20:29:01"} 
[2025-06-16 20:30:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:30:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:30:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:30:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:30:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:30:01] local.INFO: Cron job executed {"time":"2025-06-16 20:30:01"} 
[2025-06-16 20:31:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:31:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:31:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:31:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:31:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:31:01] local.INFO: Cron job executed {"time":"2025-06-16 20:31:01"} 
[2025-06-16 20:32:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:32:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:32:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:32:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:32:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:32:01] local.INFO: Cron job executed {"time":"2025-06-16 20:32:01"} 
[2025-06-16 20:33:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:33:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:33:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:33:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:33:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:33:01] local.INFO: Cron job executed {"time":"2025-06-16 20:33:01"} 
[2025-06-16 20:34:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:34:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:34:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:34:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:34:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:34:01] local.INFO: Cron job executed {"time":"2025-06-16 20:34:01"} 
[2025-06-16 20:35:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:35:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:35:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:35:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:35:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:35:01] local.INFO: Cron job executed {"time":"2025-06-16 20:35:01"} 
[2025-06-16 20:36:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:36:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:36:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:36:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:36:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:36:01] local.INFO: Cron job executed {"time":"2025-06-16 20:36:01"} 
[2025-06-16 20:37:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:37:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:37:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:37:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:37:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:37:01] local.INFO: Cron job executed {"time":"2025-06-16 20:37:01"} 
[2025-06-16 20:38:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:38:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:38:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:38:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:38:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:38:01] local.INFO: Cron job executed {"time":"2025-06-16 20:38:01"} 
[2025-06-16 20:39:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:39:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:39:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:39:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:39:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:39:01] local.INFO: Cron job executed {"time":"2025-06-16 20:39:01"} 
[2025-06-16 20:40:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:40:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:40:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:40:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:40:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:40:01] local.INFO: Cron job executed {"time":"2025-06-16 20:40:01"} 
[2025-06-16 20:41:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:41:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:41:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:41:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:41:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:41:01] local.INFO: Cron job executed {"time":"2025-06-16 20:41:01"} 
[2025-06-16 20:42:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:42:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:42:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:42:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:42:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:42:01] local.INFO: Cron job executed {"time":"2025-06-16 20:42:01"} 
[2025-06-16 20:43:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:43:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:43:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:43:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:43:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:43:01] local.INFO: Cron job executed {"time":"2025-06-16 20:43:01"} 
[2025-06-16 20:44:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:44:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:44:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:44:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:44:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:44:01] local.INFO: Cron job executed {"time":"2025-06-16 20:44:01"} 
[2025-06-16 20:45:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:45:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:45:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:45:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:45:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:45:01] local.INFO: Cron job executed {"time":"2025-06-16 20:45:01"} 
[2025-06-16 20:46:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:46:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:46:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:46:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:46:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:46:01] local.INFO: Cron job executed {"time":"2025-06-16 20:46:01"} 
[2025-06-16 20:47:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:47:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:47:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:47:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:47:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:47:01] local.INFO: Cron job executed {"time":"2025-06-16 20:47:01"} 
[2025-06-16 20:48:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:48:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:48:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:48:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:48:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:48:01] local.INFO: Cron job executed {"time":"2025-06-16 20:48:01"} 
[2025-06-16 20:49:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:49:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:49:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:49:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:49:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:49:01] local.INFO: Cron job executed {"time":"2025-06-16 20:49:01"} 
[2025-06-16 20:50:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:50:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:50:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:50:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:50:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:50:01] local.INFO: Cron job executed {"time":"2025-06-16 20:50:01"} 
[2025-06-16 20:51:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:51:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:51:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:51:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:51:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:51:01] local.INFO: Cron job executed {"time":"2025-06-16 20:51:01"} 
[2025-06-16 20:52:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:52:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:52:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:52:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:52:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:52:01] local.INFO: Cron job executed {"time":"2025-06-16 20:52:01"} 
[2025-06-16 20:53:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:53:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:53:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:53:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:53:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:53:01] local.INFO: Cron job executed {"time":"2025-06-16 20:53:01"} 
[2025-06-16 20:54:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:54:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:54:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:54:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:54:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:54:01] local.INFO: Cron job executed {"time":"2025-06-16 20:54:01"} 
[2025-06-16 20:55:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:55:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:55:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:55:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:55:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:55:01] local.INFO: Cron job executed {"time":"2025-06-16 20:55:01"} 
[2025-06-16 20:56:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:56:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:56:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:56:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:56:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:56:01] local.INFO: Cron job executed {"time":"2025-06-16 20:56:01"} 
[2025-06-16 20:57:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:57:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:57:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:57:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:57:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:57:01] local.INFO: Cron job executed {"time":"2025-06-16 20:57:01"} 
[2025-06-16 20:58:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:58:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:58:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:58:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:58:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:58:01] local.INFO: Cron job executed {"time":"2025-06-16 20:58:01"} 
[2025-06-16 20:59:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:59:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 20:59:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:59:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 20:59:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 20:59:01] local.INFO: Cron job executed {"time":"2025-06-16 20:59:01"} 
[2025-06-16 21:00:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:00:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:00:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:00:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:00:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:00:01] local.INFO: Cron job executed {"time":"2025-06-16 21:00:01"} 
[2025-06-16 21:01:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:01:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:01:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:01:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:01:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:01:01] local.INFO: Cron job executed {"time":"2025-06-16 21:01:01"} 
[2025-06-16 21:02:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:02:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:02:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:02:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:02:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:02:01] local.INFO: Cron job executed {"time":"2025-06-16 21:02:01"} 
[2025-06-16 21:03:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:03:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:03:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:03:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:03:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:03:01] local.INFO: Cron job executed {"time":"2025-06-16 21:03:01"} 
[2025-06-16 21:04:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:04:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:04:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:04:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:04:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:04:01] local.INFO: Cron job executed {"time":"2025-06-16 21:04:01"} 
[2025-06-16 21:05:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:05:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:05:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:05:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:05:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:05:01] local.INFO: Cron job executed {"time":"2025-06-16 21:05:01"} 
[2025-06-16 21:06:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:06:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:06:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:06:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:06:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:06:01] local.INFO: Cron job executed {"time":"2025-06-16 21:06:01"} 
[2025-06-16 21:07:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:07:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:07:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:07:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:07:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:07:01] local.INFO: Cron job executed {"time":"2025-06-16 21:07:01"} 
[2025-06-16 21:08:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:08:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:08:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:08:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:08:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:08:01] local.INFO: Cron job executed {"time":"2025-06-16 21:08:01"} 
[2025-06-16 21:09:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:09:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:09:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:09:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:09:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:09:01] local.INFO: Cron job executed {"time":"2025-06-16 21:09:01"} 
[2025-06-16 21:10:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:10:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:10:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:10:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:10:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:10:01] local.INFO: Cron job executed {"time":"2025-06-16 21:10:01"} 
[2025-06-16 21:11:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:11:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:11:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:11:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:11:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:11:01] local.INFO: Cron job executed {"time":"2025-06-16 21:11:01"} 
[2025-06-16 21:12:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:12:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:12:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:12:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:12:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:12:01] local.INFO: Cron job executed {"time":"2025-06-16 21:12:01"} 
[2025-06-16 21:13:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:13:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:13:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:13:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:13:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:13:01] local.INFO: Cron job executed {"time":"2025-06-16 21:13:01"} 
[2025-06-16 21:14:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:14:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:14:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:14:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:14:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:14:01] local.INFO: Cron job executed {"time":"2025-06-16 21:14:01"} 
[2025-06-16 21:15:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:15:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:15:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:15:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:15:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:15:01] local.INFO: Cron job executed {"time":"2025-06-16 21:15:01"} 
[2025-06-16 21:16:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:16:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:16:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:16:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:16:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:16:01] local.INFO: Cron job executed {"time":"2025-06-16 21:16:01"} 
[2025-06-16 21:17:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:17:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:17:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:17:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:17:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:17:01] local.INFO: Cron job executed {"time":"2025-06-16 21:17:01"} 
[2025-06-16 21:18:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:18:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:18:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:18:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:18:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:18:01] local.INFO: Cron job executed {"time":"2025-06-16 21:18:01"} 
[2025-06-16 21:19:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:19:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:19:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:19:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:19:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:19:01] local.INFO: Cron job executed {"time":"2025-06-16 21:19:01"} 
[2025-06-16 21:20:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:20:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:20:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:20:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:20:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:20:01] local.INFO: Cron job executed {"time":"2025-06-16 21:20:01"} 
[2025-06-16 21:21:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:21:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:21:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:21:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:21:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:21:01] local.INFO: Cron job executed {"time":"2025-06-16 21:21:01"} 
[2025-06-16 21:22:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:22:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:22:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:22:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:22:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:22:01] local.INFO: Cron job executed {"time":"2025-06-16 21:22:01"} 
[2025-06-16 21:23:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:23:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:23:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:23:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:23:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:23:01] local.INFO: Cron job executed {"time":"2025-06-16 21:23:01"} 
[2025-06-16 21:24:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:24:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:24:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:24:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:24:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:24:01] local.INFO: Cron job executed {"time":"2025-06-16 21:24:01"} 
[2025-06-16 21:25:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:25:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:25:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:25:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:25:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:25:01] local.INFO: Cron job executed {"time":"2025-06-16 21:25:01"} 
[2025-06-16 21:26:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:26:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:26:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:26:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:26:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:26:01] local.INFO: Cron job executed {"time":"2025-06-16 21:26:01"} 
[2025-06-16 21:27:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:27:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:27:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:27:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:27:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:27:01] local.INFO: Cron job executed {"time":"2025-06-16 21:27:01"} 
[2025-06-16 21:28:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:28:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:28:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:28:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:28:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:28:01] local.INFO: Cron job executed {"time":"2025-06-16 21:28:01"} 
[2025-06-16 21:29:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:29:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:29:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:29:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:29:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:29:01] local.INFO: Cron job executed {"time":"2025-06-16 21:29:01"} 
[2025-06-16 21:30:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:30:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:30:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:30:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:30:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:30:01] local.INFO: Cron job executed {"time":"2025-06-16 21:30:01"} 
[2025-06-16 21:31:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:31:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:31:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:31:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:31:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:31:01] local.INFO: Cron job executed {"time":"2025-06-16 21:31:01"} 
[2025-06-16 21:32:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:32:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:32:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:32:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:32:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:32:01] local.INFO: Cron job executed {"time":"2025-06-16 21:32:01"} 
[2025-06-16 21:33:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:33:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:33:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:33:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:33:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:33:01] local.INFO: Cron job executed {"time":"2025-06-16 21:33:01"} 
[2025-06-16 21:34:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:34:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:34:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:34:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:34:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:34:01] local.INFO: Cron job executed {"time":"2025-06-16 21:34:01"} 
[2025-06-16 21:35:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:35:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:35:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:35:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:35:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:35:01] local.INFO: Cron job executed {"time":"2025-06-16 21:35:01"} 
[2025-06-16 21:36:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:36:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:36:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:36:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:36:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:36:01] local.INFO: Cron job executed {"time":"2025-06-16 21:36:01"} 
[2025-06-16 21:37:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:37:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:37:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:37:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:37:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:37:01] local.INFO: Cron job executed {"time":"2025-06-16 21:37:01"} 
[2025-06-16 21:38:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:38:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:38:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:38:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:38:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:38:01] local.INFO: Cron job executed {"time":"2025-06-16 21:38:01"} 
[2025-06-16 21:39:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:39:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:39:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:39:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:39:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:39:01] local.INFO: Cron job executed {"time":"2025-06-16 21:39:01"} 
[2025-06-16 21:40:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:40:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:40:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:40:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:40:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:40:01] local.INFO: Cron job executed {"time":"2025-06-16 21:40:01"} 
[2025-06-16 21:41:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:41:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:41:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:41:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:41:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:41:01] local.INFO: Cron job executed {"time":"2025-06-16 21:41:01"} 
[2025-06-16 21:42:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:42:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:42:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:42:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:42:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:42:01] local.INFO: Cron job executed {"time":"2025-06-16 21:42:01"} 
[2025-06-16 21:43:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:43:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:43:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:43:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:43:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:43:01] local.INFO: Cron job executed {"time":"2025-06-16 21:43:01"} 
[2025-06-16 21:44:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:44:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:44:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:44:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:44:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:44:01] local.INFO: Cron job executed {"time":"2025-06-16 21:44:01"} 
[2025-06-16 21:45:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:45:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:45:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:45:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:45:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:45:01] local.INFO: Cron job executed {"time":"2025-06-16 21:45:01"} 
[2025-06-16 21:46:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:46:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:46:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:46:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:46:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:46:01] local.INFO: Cron job executed {"time":"2025-06-16 21:46:01"} 
[2025-06-16 21:47:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:47:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:47:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:47:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:47:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:47:01] local.INFO: Cron job executed {"time":"2025-06-16 21:47:01"} 
[2025-06-16 21:48:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:48:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:48:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:48:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:48:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:48:01] local.INFO: Cron job executed {"time":"2025-06-16 21:48:01"} 
[2025-06-16 21:49:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:49:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:49:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:49:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:49:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:49:01] local.INFO: Cron job executed {"time":"2025-06-16 21:49:01"} 
[2025-06-16 21:50:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:50:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:50:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:50:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:50:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:50:01] local.INFO: Cron job executed {"time":"2025-06-16 21:50:01"} 
[2025-06-16 21:51:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:51:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:51:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:51:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:51:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:51:01] local.INFO: Cron job executed {"time":"2025-06-16 21:51:01"} 
[2025-06-16 21:52:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:52:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:52:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:52:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:52:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:52:01] local.INFO: Cron job executed {"time":"2025-06-16 21:52:01"} 
[2025-06-16 21:53:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:53:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:53:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:53:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:53:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:53:01] local.INFO: Cron job executed {"time":"2025-06-16 21:53:01"} 
[2025-06-16 21:54:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:54:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:54:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:54:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:54:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:54:01] local.INFO: Cron job executed {"time":"2025-06-16 21:54:01"} 
[2025-06-16 21:55:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:55:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:55:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:55:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:55:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:55:01] local.INFO: Cron job executed {"time":"2025-06-16 21:55:01"} 
[2025-06-16 21:56:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:56:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:56:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:56:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:56:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:56:01] local.INFO: Cron job executed {"time":"2025-06-16 21:56:01"} 
[2025-06-16 21:57:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:57:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:57:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:57:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:57:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:57:01] local.INFO: Cron job executed {"time":"2025-06-16 21:57:01"} 
[2025-06-16 21:58:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:58:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:58:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:58:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:58:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:58:01] local.INFO: Cron job executed {"time":"2025-06-16 21:58:01"} 
[2025-06-16 21:59:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:59:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 21:59:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:59:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 21:59:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 21:59:01] local.INFO: Cron job executed {"time":"2025-06-16 21:59:01"} 
[2025-06-16 22:00:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:00:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:00:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:00:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:00:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:00:01] local.INFO: Cron job executed {"time":"2025-06-16 22:00:01"} 
[2025-06-16 22:01:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:01:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:01:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:01:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:01:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:01:01] local.INFO: Cron job executed {"time":"2025-06-16 22:01:01"} 
[2025-06-16 22:02:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:02:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:02:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:02:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:02:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:02:01] local.INFO: Cron job executed {"time":"2025-06-16 22:02:01"} 
[2025-06-16 22:03:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:03:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:03:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:03:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:03:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:03:01] local.INFO: Cron job executed {"time":"2025-06-16 22:03:01"} 
[2025-06-16 22:04:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:04:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:04:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:04:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:04:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:04:01] local.INFO: Cron job executed {"time":"2025-06-16 22:04:01"} 
[2025-06-16 22:05:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:05:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:05:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:05:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:05:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:05:01] local.INFO: Cron job executed {"time":"2025-06-16 22:05:01"} 
[2025-06-16 22:06:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:06:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:06:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:06:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:06:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:06:01] local.INFO: Cron job executed {"time":"2025-06-16 22:06:01"} 
[2025-06-16 22:07:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:07:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:07:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:07:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:07:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:07:01] local.INFO: Cron job executed {"time":"2025-06-16 22:07:01"} 
[2025-06-16 22:08:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:08:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:08:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:08:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:08:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:08:01] local.INFO: Cron job executed {"time":"2025-06-16 22:08:01"} 
[2025-06-16 22:09:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:09:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:09:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:09:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:09:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:09:01] local.INFO: Cron job executed {"time":"2025-06-16 22:09:01"} 
[2025-06-16 22:10:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:10:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:10:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:10:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:10:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:10:01] local.INFO: Cron job executed {"time":"2025-06-16 22:10:01"} 
[2025-06-16 22:11:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:11:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:11:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:11:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:11:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:11:01] local.INFO: Cron job executed {"time":"2025-06-16 22:11:01"} 
[2025-06-16 22:12:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:12:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:12:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:12:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:12:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:12:01] local.INFO: Cron job executed {"time":"2025-06-16 22:12:01"} 
[2025-06-16 22:13:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:13:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:13:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:13:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:13:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:13:01] local.INFO: Cron job executed {"time":"2025-06-16 22:13:01"} 
[2025-06-16 22:14:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:14:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:14:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:14:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:14:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:14:01] local.INFO: Cron job executed {"time":"2025-06-16 22:14:01"} 
[2025-06-16 22:15:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:15:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:15:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:15:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:15:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:15:01] local.INFO: Cron job executed {"time":"2025-06-16 22:15:01"} 
[2025-06-16 22:16:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:16:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:16:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:16:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:16:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:16:01] local.INFO: Cron job executed {"time":"2025-06-16 22:16:01"} 
[2025-06-16 22:17:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:17:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:17:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:17:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:17:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:17:01] local.INFO: Cron job executed {"time":"2025-06-16 22:17:01"} 
[2025-06-16 22:18:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:18:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:18:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:18:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:18:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:18:01] local.INFO: Cron job executed {"time":"2025-06-16 22:18:01"} 
[2025-06-16 22:19:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:19:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:19:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:19:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:19:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:19:01] local.INFO: Cron job executed {"time":"2025-06-16 22:19:01"} 
[2025-06-16 22:20:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:20:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:20:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:20:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:20:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:20:01] local.INFO: Cron job executed {"time":"2025-06-16 22:20:01"} 
[2025-06-16 22:21:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:21:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:21:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:21:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:21:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:21:01] local.INFO: Cron job executed {"time":"2025-06-16 22:21:01"} 
[2025-06-16 22:22:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:22:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:22:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:22:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:22:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:22:01] local.INFO: Cron job executed {"time":"2025-06-16 22:22:01"} 
[2025-06-16 22:23:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:23:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:23:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:23:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:23:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:23:01] local.INFO: Cron job executed {"time":"2025-06-16 22:23:01"} 
[2025-06-16 22:24:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:24:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:24:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:24:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:24:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:24:01] local.INFO: Cron job executed {"time":"2025-06-16 22:24:01"} 
[2025-06-16 22:25:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:25:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:25:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:25:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:25:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:25:01] local.INFO: Cron job executed {"time":"2025-06-16 22:25:01"} 
[2025-06-16 22:26:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:26:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:26:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:26:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:26:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:26:01] local.INFO: Cron job executed {"time":"2025-06-16 22:26:01"} 
[2025-06-16 22:27:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:27:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:27:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:27:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:27:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:27:01] local.INFO: Cron job executed {"time":"2025-06-16 22:27:01"} 
[2025-06-16 22:28:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:28:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:28:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:28:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:28:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:28:01] local.INFO: Cron job executed {"time":"2025-06-16 22:28:01"} 
[2025-06-16 22:29:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:29:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:29:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:29:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:29:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:29:01] local.INFO: Cron job executed {"time":"2025-06-16 22:29:01"} 
[2025-06-16 22:30:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:30:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:30:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:30:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:30:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:30:01] local.INFO: Cron job executed {"time":"2025-06-16 22:30:01"} 
[2025-06-16 22:31:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:31:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:31:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:31:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:31:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:31:01] local.INFO: Cron job executed {"time":"2025-06-16 22:31:01"} 
[2025-06-16 22:32:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:32:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:32:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:32:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:32:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:32:01] local.INFO: Cron job executed {"time":"2025-06-16 22:32:01"} 
[2025-06-16 22:33:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:33:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:33:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:33:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:33:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:33:01] local.INFO: Cron job executed {"time":"2025-06-16 22:33:01"} 
[2025-06-16 22:34:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:34:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:34:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:34:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:34:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:34:01] local.INFO: Cron job executed {"time":"2025-06-16 22:34:01"} 
[2025-06-16 22:35:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:35:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:35:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:35:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:35:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:35:01] local.INFO: Cron job executed {"time":"2025-06-16 22:35:01"} 
[2025-06-16 22:36:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:36:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:36:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:36:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:36:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:36:01] local.INFO: Cron job executed {"time":"2025-06-16 22:36:01"} 
[2025-06-16 22:37:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:37:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:37:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:37:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:37:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:37:01] local.INFO: Cron job executed {"time":"2025-06-16 22:37:01"} 
[2025-06-16 22:38:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:38:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:38:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:38:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:38:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:38:01] local.INFO: Cron job executed {"time":"2025-06-16 22:38:01"} 
[2025-06-16 22:39:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:39:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:39:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:39:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:39:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:39:01] local.INFO: Cron job executed {"time":"2025-06-16 22:39:01"} 
[2025-06-16 22:40:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:40:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:40:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:40:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:40:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:40:01] local.INFO: Cron job executed {"time":"2025-06-16 22:40:01"} 
[2025-06-16 22:41:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:41:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:41:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:41:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:41:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:41:01] local.INFO: Cron job executed {"time":"2025-06-16 22:41:01"} 
[2025-06-16 22:42:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:42:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:42:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:42:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:42:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:42:01] local.INFO: Cron job executed {"time":"2025-06-16 22:42:01"} 
[2025-06-16 22:43:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:43:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:43:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:43:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:43:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:43:01] local.INFO: Cron job executed {"time":"2025-06-16 22:43:01"} 
[2025-06-16 22:44:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:44:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:44:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:44:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:44:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:44:01] local.INFO: Cron job executed {"time":"2025-06-16 22:44:01"} 
[2025-06-16 22:45:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:45:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:45:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:45:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:45:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:45:01] local.INFO: Cron job executed {"time":"2025-06-16 22:45:01"} 
[2025-06-16 22:46:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:46:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:46:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:46:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:46:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:46:01] local.INFO: Cron job executed {"time":"2025-06-16 22:46:01"} 
[2025-06-16 22:47:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:47:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:47:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:47:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:47:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:47:01] local.INFO: Cron job executed {"time":"2025-06-16 22:47:01"} 
[2025-06-16 22:48:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:48:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:48:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:48:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:48:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:48:01] local.INFO: Cron job executed {"time":"2025-06-16 22:48:01"} 
[2025-06-16 22:49:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:49:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:49:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:49:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:49:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:49:01] local.INFO: Cron job executed {"time":"2025-06-16 22:49:01"} 
[2025-06-16 22:50:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:50:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:50:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:50:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:50:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:50:01] local.INFO: Cron job executed {"time":"2025-06-16 22:50:01"} 
[2025-06-16 22:51:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:51:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:51:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:51:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:51:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:51:01] local.INFO: Cron job executed {"time":"2025-06-16 22:51:01"} 
[2025-06-16 22:52:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:52:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:52:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:52:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:52:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:52:01] local.INFO: Cron job executed {"time":"2025-06-16 22:52:01"} 
[2025-06-16 22:53:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:53:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:53:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:53:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:53:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:53:01] local.INFO: Cron job executed {"time":"2025-06-16 22:53:01"} 
[2025-06-16 22:54:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:54:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:54:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:54:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:54:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:54:01] local.INFO: Cron job executed {"time":"2025-06-16 22:54:01"} 
[2025-06-16 22:55:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:55:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:55:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:55:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:55:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:55:01] local.INFO: Cron job executed {"time":"2025-06-16 22:55:01"} 
[2025-06-16 22:56:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:56:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:56:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:56:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:56:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:56:01] local.INFO: Cron job executed {"time":"2025-06-16 22:56:01"} 
[2025-06-16 22:57:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:57:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:57:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:57:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:57:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:57:01] local.INFO: Cron job executed {"time":"2025-06-16 22:57:01"} 
[2025-06-16 22:58:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:58:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:58:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:58:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:58:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:58:01] local.INFO: Cron job executed {"time":"2025-06-16 22:58:01"} 
[2025-06-16 22:59:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:59:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 22:59:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:59:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 22:59:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 22:59:01] local.INFO: Cron job executed {"time":"2025-06-16 22:59:01"} 
[2025-06-16 23:00:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:00:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:00:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:00:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:00:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:00:01] local.INFO: Cron job executed {"time":"2025-06-16 23:00:01"} 
[2025-06-16 23:01:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:01:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:01:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:01:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:01:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:01:01] local.INFO: Cron job executed {"time":"2025-06-16 23:01:01"} 
[2025-06-16 23:02:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:02:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:02:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:02:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:02:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:02:01] local.INFO: Cron job executed {"time":"2025-06-16 23:02:01"} 
[2025-06-16 23:03:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:03:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:03:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:03:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:03:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:03:01] local.INFO: Cron job executed {"time":"2025-06-16 23:03:01"} 
[2025-06-16 23:04:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:04:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:04:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:04:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:04:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:04:01] local.INFO: Cron job executed {"time":"2025-06-16 23:04:01"} 
[2025-06-16 23:05:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:05:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:05:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:05:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:05:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:05:01] local.INFO: Cron job executed {"time":"2025-06-16 23:05:01"} 
[2025-06-16 23:06:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:06:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:06:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:06:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:06:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:06:01] local.INFO: Cron job executed {"time":"2025-06-16 23:06:01"} 
[2025-06-16 23:07:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:07:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:07:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:07:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:07:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:07:01] local.INFO: Cron job executed {"time":"2025-06-16 23:07:01"} 
[2025-06-16 23:08:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:08:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:08:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:08:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:08:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:08:01] local.INFO: Cron job executed {"time":"2025-06-16 23:08:01"} 
[2025-06-16 23:09:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:09:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:09:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:09:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:09:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:09:01] local.INFO: Cron job executed {"time":"2025-06-16 23:09:01"} 
[2025-06-16 23:10:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:10:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:10:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:10:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:10:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:10:01] local.INFO: Cron job executed {"time":"2025-06-16 23:10:01"} 
[2025-06-16 23:11:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:11:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:11:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:11:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:11:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:11:01] local.INFO: Cron job executed {"time":"2025-06-16 23:11:01"} 
[2025-06-16 23:12:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:12:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:12:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:12:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:12:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:12:01] local.INFO: Cron job executed {"time":"2025-06-16 23:12:01"} 
[2025-06-16 23:13:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:13:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:13:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:13:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:13:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:13:01] local.INFO: Cron job executed {"time":"2025-06-16 23:13:01"} 
[2025-06-16 23:14:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:14:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:14:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:14:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:14:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:14:01] local.INFO: Cron job executed {"time":"2025-06-16 23:14:01"} 
[2025-06-16 23:15:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:15:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:15:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:15:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:15:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:15:01] local.INFO: Cron job executed {"time":"2025-06-16 23:15:01"} 
[2025-06-16 23:16:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:16:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:16:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:16:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:16:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:16:01] local.INFO: Cron job executed {"time":"2025-06-16 23:16:01"} 
[2025-06-16 23:17:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:17:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:17:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:17:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:17:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:17:01] local.INFO: Cron job executed {"time":"2025-06-16 23:17:01"} 
[2025-06-16 23:18:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:18:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:18:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:18:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:18:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:18:01] local.INFO: Cron job executed {"time":"2025-06-16 23:18:01"} 
[2025-06-16 23:19:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:19:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:19:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:19:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:19:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:19:01] local.INFO: Cron job executed {"time":"2025-06-16 23:19:01"} 
[2025-06-16 23:20:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:20:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:20:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:20:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:20:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:20:01] local.INFO: Cron job executed {"time":"2025-06-16 23:20:01"} 
[2025-06-16 23:21:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:21:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:21:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:21:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:21:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:21:01] local.INFO: Cron job executed {"time":"2025-06-16 23:21:01"} 
[2025-06-16 23:22:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:22:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:22:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:22:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:22:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:22:01] local.INFO: Cron job executed {"time":"2025-06-16 23:22:01"} 
[2025-06-16 23:23:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:23:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:23:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:23:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:23:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:23:01] local.INFO: Cron job executed {"time":"2025-06-16 23:23:01"} 
[2025-06-16 23:24:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:24:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:24:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:24:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:24:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:24:01] local.INFO: Cron job executed {"time":"2025-06-16 23:24:01"} 
[2025-06-16 23:25:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:25:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:25:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:25:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:25:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:25:01] local.INFO: Cron job executed {"time":"2025-06-16 23:25:01"} 
[2025-06-16 23:26:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:26:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:26:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:26:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:26:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:26:01] local.INFO: Cron job executed {"time":"2025-06-16 23:26:01"} 
[2025-06-16 23:27:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:27:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:27:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:27:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:27:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:27:01] local.INFO: Cron job executed {"time":"2025-06-16 23:27:01"} 
[2025-06-16 23:28:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:28:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:28:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:28:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:28:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:28:01] local.INFO: Cron job executed {"time":"2025-06-16 23:28:01"} 
[2025-06-16 23:29:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:29:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:29:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:29:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:29:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:29:01] local.INFO: Cron job executed {"time":"2025-06-16 23:29:01"} 
[2025-06-16 23:30:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:30:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:30:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:30:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:30:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:30:01] local.INFO: Cron job executed {"time":"2025-06-16 23:30:01"} 
[2025-06-16 23:31:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:31:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:31:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:31:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:31:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:31:01] local.INFO: Cron job executed {"time":"2025-06-16 23:31:01"} 
[2025-06-16 23:32:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:32:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:32:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:32:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:32:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:32:01] local.INFO: Cron job executed {"time":"2025-06-16 23:32:01"} 
[2025-06-16 23:33:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:33:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:33:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:33:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:33:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:33:01] local.INFO: Cron job executed {"time":"2025-06-16 23:33:01"} 
[2025-06-16 23:34:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:34:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:34:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:34:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:34:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:34:01] local.INFO: Cron job executed {"time":"2025-06-16 23:34:01"} 
[2025-06-16 23:35:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:35:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:35:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:35:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:35:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:35:01] local.INFO: Cron job executed {"time":"2025-06-16 23:35:01"} 
[2025-06-16 23:36:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:36:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:36:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:36:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:36:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:36:01] local.INFO: Cron job executed {"time":"2025-06-16 23:36:01"} 
[2025-06-16 23:37:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:37:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:37:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:37:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:37:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:37:01] local.INFO: Cron job executed {"time":"2025-06-16 23:37:01"} 
[2025-06-16 23:38:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:38:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:38:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:38:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:38:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:38:01] local.INFO: Cron job executed {"time":"2025-06-16 23:38:01"} 
[2025-06-16 23:39:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:39:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:39:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:39:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:39:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:39:01] local.INFO: Cron job executed {"time":"2025-06-16 23:39:01"} 
[2025-06-16 23:40:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:40:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:40:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:40:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:40:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:40:01] local.INFO: Cron job executed {"time":"2025-06-16 23:40:01"} 
[2025-06-16 23:41:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:41:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:41:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:41:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:41:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:41:01] local.INFO: Cron job executed {"time":"2025-06-16 23:41:01"} 
[2025-06-16 23:42:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:42:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:42:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:42:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:42:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:42:01] local.INFO: Cron job executed {"time":"2025-06-16 23:42:01"} 
[2025-06-16 23:43:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:43:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:43:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:43:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:43:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:43:01] local.INFO: Cron job executed {"time":"2025-06-16 23:43:01"} 
[2025-06-16 23:44:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:44:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:44:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:44:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:44:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:44:01] local.INFO: Cron job executed {"time":"2025-06-16 23:44:01"} 
[2025-06-16 23:45:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:45:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:45:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:45:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:45:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:45:01] local.INFO: Cron job executed {"time":"2025-06-16 23:45:01"} 
[2025-06-16 23:46:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:46:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:46:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:46:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:46:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:46:01] local.INFO: Cron job executed {"time":"2025-06-16 23:46:01"} 
[2025-06-16 23:47:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:47:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:47:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:47:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:47:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:47:01] local.INFO: Cron job executed {"time":"2025-06-16 23:47:01"} 
[2025-06-16 23:48:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:48:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:48:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:48:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:48:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:48:01] local.INFO: Cron job executed {"time":"2025-06-16 23:48:01"} 
[2025-06-16 23:49:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:49:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:49:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:49:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:49:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:49:01] local.INFO: Cron job executed {"time":"2025-06-16 23:49:01"} 
[2025-06-16 23:50:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:50:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:50:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:50:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:50:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:50:01] local.INFO: Cron job executed {"time":"2025-06-16 23:50:01"} 
[2025-06-16 23:51:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:51:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:51:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:51:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:51:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:51:01] local.INFO: Cron job executed {"time":"2025-06-16 23:51:01"} 
[2025-06-16 23:52:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:52:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:52:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:52:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:52:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:52:01] local.INFO: Cron job executed {"time":"2025-06-16 23:52:01"} 
[2025-06-16 23:53:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:53:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:53:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:53:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:53:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:53:01] local.INFO: Cron job executed {"time":"2025-06-16 23:53:01"} 
[2025-06-16 23:54:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:54:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:54:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:54:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:54:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:54:01] local.INFO: Cron job executed {"time":"2025-06-16 23:54:01"} 
[2025-06-16 23:55:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:55:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:55:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:55:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:55:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:55:01] local.INFO: Cron job executed {"time":"2025-06-16 23:55:01"} 
[2025-06-16 23:56:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:56:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:56:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:56:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:56:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:56:01] local.INFO: Cron job executed {"time":"2025-06-16 23:56:01"} 
[2025-06-16 23:57:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:57:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:57:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:57:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:57:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:57:01] local.INFO: Cron job executed {"time":"2025-06-16 23:57:01"} 
[2025-06-16 23:58:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:58:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:58:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:58:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:58:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:58:01] local.INFO: Cron job executed {"time":"2025-06-16 23:58:01"} 
[2025-06-16 23:59:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:59:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-16 23:59:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:59:01] local.INFO: Expired records status updated to Expired  
[2025-06-16 23:59:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-16 23:59:01] local.INFO: Cron job executed {"time":"2025-06-16 23:59:01"} 
