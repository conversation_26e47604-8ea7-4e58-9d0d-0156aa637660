{"__meta": {"id": "X227ff757c4e41e4004c8eae1e99bb0eb", "datetime": "2025-06-18 01:24:49", "utime": **********.603857, "method": "POST", "uri": "/api/v1/orders", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 1, "messages": [{"message": "[01:24:49] LOG.error: Attempted to lazy load [restrictions] on model [App\\Models\\Ticket] but lazy loading is disabled. {\n    \"userId\": 7,\n    \"exception\": {\n        \"model\": \"App\\\\Models\\\\Ticket\",\n        \"relation\": \"restrictions\"\n    }\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.599229, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.714845, "end": **********.603885, "duration": 0.8890399932861328, "duration_str": "889ms", "measures": [{"label": "Booting", "start": **********.714845, "relative_start": 0, "end": **********.777664, "relative_end": **********.777664, "duration": 0.06281900405883789, "duration_str": "62.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.77768, "relative_start": 0.0628349781036377, "end": **********.603909, "relative_end": 2.4080276489257812e-05, "duration": 0.8262290954589844, "duration_str": "826ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 6732464, "peak_usage_str": "6MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "Illuminate\\Database\\LazyLoadingViolationException", "message": "Attempted to lazy load [restrictions] on model [App\\Models\\Ticket] but lazy loading is disabled.", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/HasAttributes.php", "line": 595, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:94</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"84 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/HasAttributes.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>554</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"26 characters\">handleLazyLoadingViolation</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Database\\Eloquent\\Model</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">restrictions</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"84 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/HasAttributes.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>485</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"16 characters\">getRelationValue</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Database\\Eloquent\\Model</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">restrictions</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"67 characters\">vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>2260</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">getAttribute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Database\\Eloquent\\Model</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">restrictions</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"36 characters\">app/Http/Resources/OrderResource.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>47</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">__get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Database\\Eloquent\\Model</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">restrictions</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Collections/helpers.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>236</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"56 characters\">{closure:App\\Http\\Resources\\OrderResource::toArray():46}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Http\\Resources\\OrderResource</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">[object App\\Models\\Ticket]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Http/Resources/ConditionallyLoadsAttributes.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>283</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">value</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"26 characters\">[object App\\Models\\Ticket]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"36 characters\">app/Http/Resources/OrderResource.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>46</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">whenLoaded</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Resources\\Json\\JsonResource</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">ticket</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>108</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">toArray</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Http\\Resources\\OrderResource</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>255</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Resources\\Json\\JsonResource</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"79 characters\">vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>960</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">jsonSerialize</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Resources\\Json\\JsonResource</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"73 characters\">{closure:Illuminate\\Support\\Traits\\EnumeratesValues::jsonSerialize():958}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Support\\Collection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">[object App\\Http\\Resources\\OrderResource]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"79 characters\">vendor/laravel/framework/src/Illuminate/Collections/Traits/EnumeratesValues.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>958</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"9 characters\">array_map</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Http\\Resources\\OrderResource\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Http\\Resources</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>OrderResource</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22916 title=\"2 occurrences\">#2916</a><samp data-depth=5 id=sf-dump-**********-ref22916 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">resource</span>: <span class=sf-dump-note title=\"App\\Models\\Order\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Order</span> {<a class=sf-dump-ref>#2707</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">orders</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n            +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n            #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n            +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>true</span>\n            #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n            +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n            +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>12</span>\n              \"<span class=sf-dump-key>order_no</span>\" => \"<span class=sf-dump-str title=\"6 characters\">TGO012</span>\"\n              \"<span class=sf-dump-key>buyer_id</span>\" => <span class=sf-dump-num>7</span>\n              \"<span class=sf-dump-key>ticket_id</span>\" => <span class=sf-dump-num>8</span>\n              \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n              \"<span class=sf-dump-key>total_price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">443.68</span>\"\n              \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">confirmed</span>\"\n              \"<span class=sf-dump-key>purchase_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-16</span>\"\n              \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>7</span>\n              \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 15:08:27</span>\"\n              \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 15:09:12</span>\"\n              \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>12</span>\n              \"<span class=sf-dump-key>order_no</span>\" => \"<span class=sf-dump-str title=\"6 characters\">TGO012</span>\"\n              \"<span class=sf-dump-key>buyer_id</span>\" => <span class=sf-dump-num>7</span>\n              \"<span class=sf-dump-key>ticket_id</span>\" => <span class=sf-dump-num>8</span>\n              \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n              \"<span class=sf-dump-key>total_price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">443.68</span>\"\n              \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">confirmed</span>\"\n              \"<span class=sf-dump-key>purchase_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-16</span>\"\n              \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>7</span>\n              \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 15:08:27</span>\"\n              \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 15:09:12</span>\"\n              \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Enums\\OrderStatus</span>\"\n              \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>ticket</span>\" => <span class=sf-dump-note title=\"App\\Models\\Ticket\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Ticket</span> {<a class=sf-dump-ref>#2761</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"7 characters\">tickets</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>8</span>\n                  \"<span class=sf-dump-key>event_id</span>\" => <span class=sf-dump-num>7</span>\n                  \"<span class=sf-dump-key>ticket_no</span>\" => \"<span class=sf-dump-str title=\"6 characters\">TGT008</span>\"\n                  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">443.68</span>\"\n                  \"<span class=sf-dump-key>sector_id</span>\" => <span class=sf-dump-num>45</span>\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>8</span>\n                  \"<span class=sf-dump-key>event_id</span>\" => <span class=sf-dump-num>7</span>\n                  \"<span class=sf-dump-key>ticket_no</span>\" => \"<span class=sf-dump-str title=\"6 characters\">TGT008</span>\"\n                  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">443.68</span>\"\n                  \"<span class=sf-dump-key>sector_id</span>\" => <span class=sf-dump-num>45</span>\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>ticket_type</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Enums\\TicketType</span>\"\n                  \"<span class=sf-dump-key>quantity_split_type</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Enums\\TicketQuantitySplitType</span>\"\n                  \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>event</span>\" => <span class=sf-dump-note title=\"App\\Models\\Event\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Event</span> {<a class=sf-dump-ref>#2824</a><samp data-depth=10 class=sf-dump-compact>\n                    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">events</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>true</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n                      \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-02</span>\"\n                      \"<span class=sf-dump-key>time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">04:12:24</span>\"\n                      \"<span class=sf-dump-key>timezone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Europe/Malta</span>\"\n                      \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"7 characters\">concert</span>\"\n                      \"<span class=sf-dump-key>country_id</span>\" => <span class=sf-dump-num>211</span>\n                      \"<span class=sf-dump-key>home_club_id</span>\" => <span class=sf-dump-num>1</span>\n                      \"<span class=sf-dump-key>guest_club_id</span>\" => <span class=sf-dump-num>5</span>\n                      \"<span class=sf-dump-key>stadium_id</span>\" => <span class=sf-dump-num>5</span>\n                      \"<span class=sf-dump-key>league_id</span>\" => <span class=sf-dump-num>4</span>\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n                      \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-02</span>\"\n                      \"<span class=sf-dump-key>time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">04:12:24</span>\"\n                      \"<span class=sf-dump-key>timezone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Europe/Malta</span>\"\n                      \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"7 characters\">concert</span>\"\n                      \"<span class=sf-dump-key>country_id</span>\" => <span class=sf-dump-num>211</span>\n                      \"<span class=sf-dump-key>home_club_id</span>\" => <span class=sf-dump-num>1</span>\n                      \"<span class=sf-dump-key>guest_club_id</span>\" => <span class=sf-dump-num>5</span>\n                      \"<span class=sf-dump-key>stadium_id</span>\" => <span class=sf-dump-num>5</span>\n                      \"<span class=sf-dump-key>league_id</span>\" => <span class=sf-dump-num>4</span>\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Enums\\EventCategoryType</span>\"\n                      \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n                      \"<span class=sf-dump-key>time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                      \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>translation</span>\" => <span class=sf-dump-note title=\"App\\Models\\EventTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>EventTranslation</span> {<a class=sf-dump-ref>#2869</a><samp data-depth=12 class=sf-dump-compact>\n                        #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"18 characters\">event_translations</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                        +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>true</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                        +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                        +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>19</span>\n                          \"<span class=sf-dump-key>event_id</span>\" => <span class=sf-dump-num>7</span>\n                          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Jaunita Runolfsdottir</span>\"\n                          \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Earum vel ex et quo rem odio amet inventore. Sint ut quasi et nam sed.</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>19</span>\n                          \"<span class=sf-dump-key>event_id</span>\" => <span class=sf-dump-num>7</span>\n                          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Jaunita Runolfsdottir</span>\"\n                          \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Earum vel ex et quo rem odio amet inventore. Sint ut quasi et nam sed.</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                        +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">event_id</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">locale</span>\"\n                          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                          <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"\n                          <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"\n                          <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"13 characters\">meta_keywords</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">oldAttributes</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">activitylogOptions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? ?Spatie\\Activitylog\\LogOptions</span>\n                        +<span class=sf-dump-public title=\"Public property\">enableLoggingModelsEvents</span>: <span class=sf-dump-const>true</span>\n                      </samp>}\n                      \"<span class=sf-dump-key>localizedSlug</span>\" => <span class=sf-dump-note title=\"App\\Models\\Slug\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Slug</span> {<a class=sf-dump-ref>#2912</a><samp data-depth=12 class=sf-dump-compact>\n                        #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">slugs</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                        +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>true</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                        +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                        +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>sluggable_id</span>\" => <span class=sf-dump-num>7</span>\n                          \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"29 characters\">event-jaunita-runolfsdottir-7</span>\"\n                          \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>sluggable_id</span>\" => <span class=sf-dump-num>7</span>\n                          \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"29 characters\">event-jaunita-runolfsdottir-7</span>\"\n                          \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                        +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">locale</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                        </samp>]\n                      </samp>}\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                    +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">guard</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">oldAttributes</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">activitylogOptions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? ?Spatie\\Activitylog\\LogOptions</span>\n                    +<span class=sf-dump-public title=\"Public property\">enableLoggingModelsEvents</span>: <span class=sf-dump-const>true</span>\n                    +<span class=sf-dump-public title=\"Public property\">mediaConversions</span>: []\n                    +<span class=sf-dump-public title=\"Public property\">mediaCollections</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">deletePreservingMedia</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">unAttachedMediaLibraryItems</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n                  </samp>}\n                  \"<span class=sf-dump-key>sector</span>\" => <span class=sf-dump-note title=\"App\\Models\\StadiumSector\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>StadiumSector</span> {<a class=sf-dump-ref>#2982</a><samp data-depth=10 class=sf-dump-compact>\n                    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"15 characters\">stadium_sectors</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>true</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>45</span>\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>45</span>\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>translation</span>\" => <span class=sf-dump-note title=\"App\\Models\\StadiumSectorTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>StadiumSectorTranslation</span> {<a class=sf-dump-ref>#3027</a><samp data-depth=12 class=sf-dump-compact>\n                        #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"27 characters\">stadium_sector_translations</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                        +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>true</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                        +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                        +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>stadium_sector_id</span>\" => <span class=sf-dump-num>45</span>\n                          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"25 characters\">DarkTurquoise - 339730659</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>stadium_sector_id</span>\" => <span class=sf-dump-num>45</span>\n                          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"25 characters\">DarkTurquoise - 339730659</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                        +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">stadium_sector_id</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">locale</span>\"\n                          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">oldAttributes</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">activitylogOptions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? ?Spatie\\Activitylog\\LogOptions</span>\n                        +<span class=sf-dump-public title=\"Public property\">enableLoggingModelsEvents</span>: <span class=sf-dump-const>true</span>\n                      </samp>}\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                    +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">oldAttributes</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">activitylogOptions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? ?Spatie\\Activitylog\\LogOptions</span>\n                    +<span class=sf-dump-public title=\"Public property\">enableLoggingModelsEvents</span>: <span class=sf-dump-const>true</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n                  </samp>}\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">oldAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">activitylogOptions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? ?Spatie\\Activitylog\\LogOptions</span>\n                +<span class=sf-dump-public title=\"Public property\">enableLoggingModelsEvents</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n              </samp>}\n              \"<span class=sf-dump-key>attendees</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#2988</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Models\\Attendee\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Attendee</span> {<a class=sf-dump-ref>#3074</a><samp data-depth=10 class=sf-dump-compact>\n                    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">attendees</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>true</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>76</span>\n                      \"<span class=sf-dump-key>order_id</span>\" => <span class=sf-dump-num>12</span>\n                      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">ASFwsed</span>\"\n                      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"13 characters\"><EMAIL></span>\"\n                      \"<span class=sf-dump-key>gender</span>\" => \"<span class=sf-dump-str title=\"4 characters\">male</span>\"\n                      \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2000-09-09</span>\"\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>76</span>\n                      \"<span class=sf-dump-key>order_id</span>\" => <span class=sf-dump-num>12</span>\n                      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">ASFwsed</span>\"\n                      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"13 characters\"><EMAIL></span>\"\n                      \"<span class=sf-dump-key>gender</span>\" => \"<span class=sf-dump-str title=\"4 characters\">male</span>\"\n                      \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2000-09-09</span>\"\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                    +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                    </samp>]\n                  </samp>}\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              </samp>}\n              \"<span class=sf-dump-key>transactions</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#2895</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Models\\OrderTransaction\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>OrderTransaction</span> {<a class=sf-dump-ref>#3125</a><samp data-depth=10 class=sf-dump-compact>\n                    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"18 characters\">order_transactions</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>true</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n                      \"<span class=sf-dump-key>order_id</span>\" => <span class=sf-dump-num>12</span>\n                      \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                      \"<span class=sf-dump-key>currency_code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">inr</span>\"\n                      \"<span class=sf-dump-key>total_amount</span>\" => \"<span class=sf-dump-str title=\"8 characters\">56722.29</span>\"\n                      \"<span class=sf-dump-key>paid_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 15:09:12</span>\"\n                      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 15:08:28</span>\"\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n                      \"<span class=sf-dump-key>order_id</span>\" => <span class=sf-dump-num>12</span>\n                      \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                      \"<span class=sf-dump-key>currency_code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">inr</span>\"\n                      \"<span class=sf-dump-key>total_amount</span>\" => \"<span class=sf-dump-str title=\"8 characters\">56722.29</span>\"\n                      \"<span class=sf-dump-key>paid_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 15:09:12</span>\"\n                      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 15:08:28</span>\"\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Enums\\OrderTransactionStatus</span>\"\n                      \"<span class=sf-dump-key>paid_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                      \"<span class=sf-dump-key>refunded_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                    +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                  </samp>}\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              </samp>}\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n            +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n            +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">oldAttributes</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">activitylogOptions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? ?Spatie\\Activitylog\\LogOptions</span>\n            +<span class=sf-dump-public title=\"Public property\">enableLoggingModelsEvents</span>: <span class=sf-dump-const>true</span>\n            #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">with</span>: []\n          +<span class=sf-dump-public title=\"Public property\">additional</span>: []\n        </samp>}\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"App\\Http\\Resources\\OrderResource\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Http\\Resources</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>OrderResource</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22644 title=\"2 occurrences\">#2644</a><samp data-depth=5 id=sf-dump-**********-ref22644 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">resource</span>: <span class=sf-dump-note title=\"App\\Models\\Order\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Order</span> {<a class=sf-dump-ref>#2696</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">orders</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n            +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n            #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n            +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>true</span>\n            #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n            +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n            +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>\n              \"<span class=sf-dump-key>order_no</span>\" => \"<span class=sf-dump-str title=\"6 characters\">TGO011</span>\"\n              \"<span class=sf-dump-key>buyer_id</span>\" => <span class=sf-dump-num>7</span>\n              \"<span class=sf-dump-key>ticket_id</span>\" => <span class=sf-dump-num>6</span>\n              \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n              \"<span class=sf-dump-key>total_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">42.43</span>\"\n              \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">expired</span>\"\n              \"<span class=sf-dump-key>purchase_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-16</span>\"\n              \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>7</span>\n              \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 14:52:42</span>\"\n              \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 15:13:02</span>\"\n              \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>\n              \"<span class=sf-dump-key>order_no</span>\" => \"<span class=sf-dump-str title=\"6 characters\">TGO011</span>\"\n              \"<span class=sf-dump-key>buyer_id</span>\" => <span class=sf-dump-num>7</span>\n              \"<span class=sf-dump-key>ticket_id</span>\" => <span class=sf-dump-num>6</span>\n              \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n              \"<span class=sf-dump-key>total_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">42.43</span>\"\n              \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">expired</span>\"\n              \"<span class=sf-dump-key>purchase_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-16</span>\"\n              \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>7</span>\n              \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 14:52:42</span>\"\n              \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 15:13:02</span>\"\n              \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Enums\\OrderStatus</span>\"\n              \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>ticket</span>\" => <span class=sf-dump-note title=\"App\\Models\\Ticket\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Ticket</span> {<a class=sf-dump-ref>#2742</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"7 characters\">tickets</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>6</span>\n                  \"<span class=sf-dump-key>event_id</span>\" => <span class=sf-dump-num>5</span>\n                  \"<span class=sf-dump-key>ticket_no</span>\" => \"<span class=sf-dump-str title=\"6 characters\">TGT006</span>\"\n                  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">42.43</span>\"\n                  \"<span class=sf-dump-key>sector_id</span>\" => <span class=sf-dump-num>10</span>\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>6</span>\n                  \"<span class=sf-dump-key>event_id</span>\" => <span class=sf-dump-num>5</span>\n                  \"<span class=sf-dump-key>ticket_no</span>\" => \"<span class=sf-dump-str title=\"6 characters\">TGT006</span>\"\n                  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">42.43</span>\"\n                  \"<span class=sf-dump-key>sector_id</span>\" => <span class=sf-dump-num>10</span>\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>ticket_type</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Enums\\TicketType</span>\"\n                  \"<span class=sf-dump-key>quantity_split_type</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Enums\\TicketQuantitySplitType</span>\"\n                  \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>event</span>\" => <span class=sf-dump-note title=\"App\\Models\\Event\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Event</span> {<a class=sf-dump-ref>#2805</a><samp data-depth=10 class=sf-dump-compact>\n                    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">events</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>true</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                      \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-11</span>\"\n                      \"<span class=sf-dump-key>time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">22:52:33</span>\"\n                      \"<span class=sf-dump-key>timezone</span>\" => \"<span class=sf-dump-str title=\"26 characters\">America/Argentina/San_Luis</span>\"\n                      \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"7 characters\">concert</span>\"\n                      \"<span class=sf-dump-key>country_id</span>\" => <span class=sf-dump-num>112</span>\n                      \"<span class=sf-dump-key>home_club_id</span>\" => <span class=sf-dump-num>9</span>\n                      \"<span class=sf-dump-key>guest_club_id</span>\" => <span class=sf-dump-num>4</span>\n                      \"<span class=sf-dump-key>stadium_id</span>\" => <span class=sf-dump-num>1</span>\n                      \"<span class=sf-dump-key>league_id</span>\" => <span class=sf-dump-num>7</span>\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                      \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-11</span>\"\n                      \"<span class=sf-dump-key>time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">22:52:33</span>\"\n                      \"<span class=sf-dump-key>timezone</span>\" => \"<span class=sf-dump-str title=\"26 characters\">America/Argentina/San_Luis</span>\"\n                      \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"7 characters\">concert</span>\"\n                      \"<span class=sf-dump-key>country_id</span>\" => <span class=sf-dump-num>112</span>\n                      \"<span class=sf-dump-key>home_club_id</span>\" => <span class=sf-dump-num>9</span>\n                      \"<span class=sf-dump-key>guest_club_id</span>\" => <span class=sf-dump-num>4</span>\n                      \"<span class=sf-dump-key>stadium_id</span>\" => <span class=sf-dump-num>1</span>\n                      \"<span class=sf-dump-key>league_id</span>\" => <span class=sf-dump-num>7</span>\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"27 characters\">App\\Enums\\EventCategoryType</span>\"\n                      \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n                      \"<span class=sf-dump-key>time</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                      \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>translation</span>\" => <span class=sf-dump-note title=\"App\\Models\\EventTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>EventTranslation</span> {<a class=sf-dump-ref>#2840</a><samp data-depth=12 class=sf-dump-compact>\n                        #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"18 characters\">event_translations</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                        +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>true</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                        +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                        +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>13</span>\n                          \"<span class=sf-dump-key>event_id</span>\" => <span class=sf-dump-num>5</span>\n                          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Westley Eichmann</span>\"\n                          \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"161 characters\">Dolores nisi et voluptates nihil recusandae minima. Excepturi dolores inventore consectetur perferendis voluptas et. Nobis voluptatem sed earum harum nostrum in.</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>13</span>\n                          \"<span class=sf-dump-key>event_id</span>\" => <span class=sf-dump-num>5</span>\n                          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Westley Eichmann</span>\"\n                          \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"161 characters\">Dolores nisi et voluptates nihil recusandae minima. Excepturi dolores inventore consectetur perferendis voluptas et. Nobis voluptatem sed earum harum nostrum in.</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                        +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">event_id</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">locale</span>\"\n                          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                          <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">meta_title</span>\"\n                          <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"16 characters\">meta_description</span>\"\n                          <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"13 characters\">meta_keywords</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">oldAttributes</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">activitylogOptions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? ?Spatie\\Activitylog\\LogOptions</span>\n                        +<span class=sf-dump-public title=\"Public property\">enableLoggingModelsEvents</span>: <span class=sf-dump-const>true</span>\n                      </samp>}\n                      \"<span class=sf-dump-key>localizedSlug</span>\" => <span class=sf-dump-note title=\"App\\Models\\Slug\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Slug</span> {<a class=sf-dump-ref>#2825</a><samp data-depth=12 class=sf-dump-compact>\n                        #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">slugs</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                        +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>true</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                        +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                        +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>sluggable_id</span>\" => <span class=sf-dump-num>5</span>\n                          \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"24 characters\">event-westley-eichmann-5</span>\"\n                          \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>sluggable_id</span>\" => <span class=sf-dump-num>5</span>\n                          \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"24 characters\">event-westley-eichmann-5</span>\"\n                          \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                        +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">locale</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                        </samp>]\n                      </samp>}\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                    +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">guard</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">oldAttributes</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">activitylogOptions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? ?Spatie\\Activitylog\\LogOptions</span>\n                    +<span class=sf-dump-public title=\"Public property\">enableLoggingModelsEvents</span>: <span class=sf-dump-const>true</span>\n                    +<span class=sf-dump-public title=\"Public property\">mediaConversions</span>: []\n                    +<span class=sf-dump-public title=\"Public property\">mediaCollections</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">deletePreservingMedia</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">unAttachedMediaLibraryItems</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n                  </samp>}\n                  \"<span class=sf-dump-key>sector</span>\" => <span class=sf-dump-note title=\"App\\Models\\StadiumSector\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>StadiumSector</span> {<a class=sf-dump-ref>#2963</a><samp data-depth=10 class=sf-dump-compact>\n                    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"15 characters\">stadium_sectors</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>true</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>translation</span>\" => <span class=sf-dump-note title=\"App\\Models\\StadiumSectorTranslation\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>StadiumSectorTranslation</span> {<a class=sf-dump-ref>#2998</a><samp data-depth=12 class=sf-dump-compact>\n                        #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"27 characters\">stadium_sector_translations</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                        #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                        +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>true</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                        +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                        +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>stadium_sector_id</span>\" => <span class=sf-dump-num>10</span>\n                          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">GoldenRod - 403153505</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=13 class=sf-dump-compact>\n                          \"<span class=sf-dump-key>stadium_sector_id</span>\" => <span class=sf-dump-num>10</span>\n                          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">GoldenRod - 403153505</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                        +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                        +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                        #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">stadium_sector_id</span>\"\n                          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">locale</span>\"\n                          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=13 class=sf-dump-compact>\n                          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                        </samp>]\n                        #<span class=sf-dump-protected title=\"Protected property\">oldAttributes</span>: []\n                        #<span class=sf-dump-protected title=\"Protected property\">activitylogOptions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? ?Spatie\\Activitylog\\LogOptions</span>\n                        +<span class=sf-dump-public title=\"Public property\">enableLoggingModelsEvents</span>: <span class=sf-dump-const>true</span>\n                      </samp>}\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                    +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">oldAttributes</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">activitylogOptions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? ?Spatie\\Activitylog\\LogOptions</span>\n                    +<span class=sf-dump-public title=\"Public property\">enableLoggingModelsEvents</span>: <span class=sf-dump-const>true</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n                  </samp>}\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">oldAttributes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">activitylogOptions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? ?Spatie\\Activitylog\\LogOptions</span>\n                +<span class=sf-dump-public title=\"Public property\">enableLoggingModelsEvents</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n              </samp>}\n              \"<span class=sf-dump-key>attendees</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#2999</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Models\\Attendee\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Attendee</span> {<a class=sf-dump-ref>#2989</a><samp data-depth=10 class=sf-dump-compact>\n                    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">attendees</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>true</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>75</span>\n                      \"<span class=sf-dump-key>order_id</span>\" => <span class=sf-dump-num>11</span>\n                      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">qertgqer</span>\"\n                      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"15 characters\"><EMAIL></span>\"\n                      \"<span class=sf-dump-key>gender</span>\" => \"<span class=sf-dump-str title=\"4 characters\">male</span>\"\n                      \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2001-09-21</span>\"\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>75</span>\n                      \"<span class=sf-dump-key>order_id</span>\" => <span class=sf-dump-num>11</span>\n                      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">qertgqer</span>\"\n                      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"15 characters\"><EMAIL></span>\"\n                      \"<span class=sf-dump-key>gender</span>\" => \"<span class=sf-dump-str title=\"4 characters\">male</span>\"\n                      \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2001-09-21</span>\"\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                    +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=11 class=sf-dump-compact>\n                      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                    </samp>]\n                  </samp>}\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              </samp>}\n              \"<span class=sf-dump-key>transactions</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#3080</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Models\\OrderTransaction\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>OrderTransaction</span> {<a class=sf-dump-ref>#3077</a><samp data-depth=10 class=sf-dump-compact>\n                    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"18 characters\">order_transactions</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>true</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n                      \"<span class=sf-dump-key>order_id</span>\" => <span class=sf-dump-num>11</span>\n                      \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">expired</span>\"\n                      \"<span class=sf-dump-key>currency_code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">eur</span>\"\n                      \"<span class=sf-dump-key>total_amount</span>\" => <span class=sf-dump-const>null</span>\n                      \"<span class=sf-dump-key>paid_at</span>\" => <span class=sf-dump-const>null</span>\n                      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 14:52:50</span>\"\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n                      \"<span class=sf-dump-key>order_id</span>\" => <span class=sf-dump-num>11</span>\n                      \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">expired</span>\"\n                      \"<span class=sf-dump-key>currency_code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">eur</span>\"\n                      \"<span class=sf-dump-key>total_amount</span>\" => <span class=sf-dump-const>null</span>\n                      \"<span class=sf-dump-key>paid_at</span>\" => <span class=sf-dump-const>null</span>\n                      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 14:52:50</span>\"\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=11 class=sf-dump-compact>\n                      \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Enums\\OrderTransactionStatus</span>\"\n                      \"<span class=sf-dump-key>paid_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                      \"<span class=sf-dump-key>refunded_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                    </samp>]\n                    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                    +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n                    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: []\n                  </samp>}\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              </samp>}\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n            +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n            +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">oldAttributes</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">activitylogOptions</span>: <span class=sf-dump-const title=\"Uninitialized property\">? ?Spatie\\Activitylog\\LogOptions</span>\n            +<span class=sf-dump-public title=\"Public property\">enableLoggingModelsEvents</span>: <span class=sf-dump-const>true</span>\n            #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">with</span>: []\n          +<span class=sf-dump-public title=\"Public property\">additional</span>: []\n        </samp>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">jsonSerialize</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Support\\Collection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>86</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">json_encode</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>success</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Success</span>\"\n        \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note title=\"App\\Http\\Resources\\OrderCollection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Http\\Resources</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>OrderCollection</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22980 title=\"5 occurrences\">#2980</a><samp data-depth=5 id=sf-dump-**********-ref22980 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">resource</span>: <span class=sf-dump-note title=\"Illuminate\\Pagination\\LengthAwarePaginator\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pagination</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>LengthAwarePaginator</span> {<a class=sf-dump-ref>#2925</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22645 title=\"2 occurrences\">#2645</a><samp data-depth=7 id=sf-dump-**********-ref22645 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Http\\Resources\\OrderResource\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Http\\Resources</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>OrderResource</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22916 title=\"2 occurrences\">#2916</a>}\n                <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"App\\Http\\Resources\\OrderResource\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Http\\Resources</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>OrderResource</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22644 title=\"2 occurrences\">#2644</a>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>9</span>\n            #<span class=sf-dump-protected title=\"Protected property\">currentPage</span>: <span class=sf-dump-num>1</span>\n            #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"35 characters\">http://ticketgol.test/api/v1/orders</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">query</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">fragment</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">pageName</span>: \"<span class=sf-dump-str title=\"4 characters\">page</span>\"\n            +<span class=sf-dump-public title=\"Public property\">onEachSide</span>: <span class=sf-dump-num>3</span>\n            #<span class=sf-dump-protected title=\"Protected property\">options</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://ticketgol.test/api/v1/orders</span>\"\n              \"<span class=sf-dump-key>pageName</span>\" => \"<span class=sf-dump-str title=\"4 characters\">page</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">total</span>: <span class=sf-dump-num>2</span>\n            #<span class=sf-dump-protected title=\"Protected property\">lastPage</span>: <span class=sf-dump-num>1</span>\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">with</span>: []\n          +<span class=sf-dump-public title=\"Public property\">additional</span>: []\n          +<span class=sf-dump-public title=\"Public property\">collects</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">collection</span>: <span class=sf-dump-note title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22645 title=\"2 occurrences\">#2645</a>}\n          #<span class=sf-dump-protected title=\"Protected property\">preserveAllQueryParameters</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">queryParameters</span>: <span class=sf-dump-const>null</span>\n        </samp>}\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"47 characters\">vendor/symfony/http-foundation/JsonResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">setData</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Http\\JsonResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>success</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Success</span>\"\n        \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note title=\"App\\Http\\Resources\\OrderCollection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Http\\Resources</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>OrderCollection</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22980 title=\"5 occurrences\">#2980</a>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Http/JsonResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>32</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Symfony\\Component\\HttpFoundation\\JsonResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>success</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Success</span>\"\n        \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note title=\"App\\Http\\Resources\\OrderCollection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Http\\Resources</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>OrderCollection</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22980 title=\"5 occurrences\">#2980</a>}\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => []\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-const>false</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"67 characters\">vendor/laravel/framework/src/Illuminate/Routing/ResponseFactory.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>102</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Http\\JsonResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>success</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Success</span>\"\n        \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note title=\"App\\Http\\Resources\\OrderCollection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Http\\Resources</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>OrderCollection</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22980 title=\"5 occurrences\">#2980</a>}\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => []\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"27 characters\">app/Helpers/ApiResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>11</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">json</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Routing\\ResponseFactory</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>success</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Success</span>\"\n        \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note title=\"App\\Http\\Resources\\OrderCollection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Http\\Resources</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>OrderCollection</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22980 title=\"5 occurrences\">#2980</a>}\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"47 characters\">app/Http/Controllers/Api/V1/OrderController.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>26</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Helpers\\ApiResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">[object App\\Http\\Resources\\OrderCollection]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">SUCCESS</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>47</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">App\\Http\\Controllers\\Api\\V1\\OrderController</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">[object App\\Http\\Requests\\Api\\V1\\OrderFilterRequest]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"102 characters\">vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"52 characters\">[object App\\Http\\Controllers\\Api\\V1\\OrderController]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"92 characters\">vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"90 characters\">{closure:Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing::dispatch():20}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"102 characters\">vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>20</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"17 characters\">wrapRouteDispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>266</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"52 characters\">[object App\\Http\\Controllers\\Api\\V1\\OrderController]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>212</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">runController</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>808</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"62 characters\">{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"47 characters\">app/Http/Middleware/Api/SetLocaleFromHeader.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"64 characters\">{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">App\\Http\\Middleware\\Api\\SetLocaleFromHeader</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">sanctum</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>25</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"88 characters\">{closure:Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful::handle():24}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"64 characters\">{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"51 characters\">Laravel\\Sanctum\\Http\\Middleware\\AuthenticateSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>88</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>60</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>165</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"100 characters\">{closure:Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful::frontendMiddleware():57}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>24</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>201</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"67 characters\">{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">vendor/sentry/sentry-laravel/src/Sentry/Laravel/Http/FlushEventsMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>13</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"64 characters\">{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Sentry\\Laravel\\Http\\FlushEventsMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"79 characters\">vendor/sentry/sentry-laravel/src/Sentry/Laravel/Http/SetRequestIpMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>45</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Sentry\\Laravel\\Http\\SetRequestIpMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"77 characters\">vendor/sentry/sentry-laravel/src/Sentry/Laravel/Http/SetRequestMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Sentry\\Laravel\\Http\\SetRequestMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"106 characters\">vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>19</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>65</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"82 characters\">Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>66</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/beyondcode/laravel-query-detector/src/QueryDetectorMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>33</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>67</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">BeyondCode\\QueryDetector\\QueryDetectorMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>68</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>69</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>70</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>71</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>72</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>73</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>74</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>75</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>76</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>77</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>78</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>79</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>80</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>62</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>81</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>82</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>83</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>84</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>85</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>86</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Middleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>79</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>87</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Sentry\\Laravel\\Tracing\\Middleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>88</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"65 characters\">{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>89</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>90</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>145</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>91</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1190</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>92</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">handleRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>93</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">C:/Program Files/Herd/resources/app.asar.unpacked/resources/valet/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>139</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">E:\\Ticketgol\\Code\\Ticketgol\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">require</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            return;\n", "        }\n", "\n", "        throw new LazyLoadingViolationException($this, $key);\n", "    }\n", "\n", "    /**\n"], "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FDatabase%2FEloquent%2FConcerns%2FHasAttributes.php&line=595", "ajax": false, "filename": "HasAttributes.php", "line": "595"}}]}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/v1/orders", "middleware": "api, set-locale, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\V1\\OrderController@index", "namespace": null, "where": [], "as": "api.orders.index", "prefix": "api/v1/orders", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FOrderController.php&line=21\" onclick=\"\">app/Http/Controllers/Api/V1/OrderController.php:21-27</a>"}, "queries": {"nb_statements": 13, "nb_visible_statements": 13, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.019249999999999996, "accumulated_duration_str": "19.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'lozOkcXmaVGi8C7rcRTIGiWNVnpFNp5J1rBvucUG' limit 1", "type": "query", "params": [], "bindings": ["lozOkcXmaVGi8C7rcRTIGiWNVnpFNp5J1rBvucUG"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.782273, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 4.519}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "auth.session", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.786023, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 4.519, "width_percent": 3.948}, {"sql": "select count(*) as aggregate from `orders` where `buyer_id` = 7 and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 24}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.791658, "duration": 0.00285, "duration_str": "2.85ms", "memory": 0, "memory_str": null, "filename": "OrderService.php:27", "source": {"index": 16, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=27", "ajax": false, "filename": "OrderService.php", "line": "27"}, "connection": "ticketgol", "explain": null, "start_percent": 8.468, "width_percent": 14.805}, {"sql": "select * from `orders` where `buyer_id` = 7 and `orders`.`deleted_at` is null order by `created_at` desc limit 9 offset 0", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 24}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.796308, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "OrderService.php:27", "source": {"index": 16, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=27", "ajax": false, "filename": "OrderService.php", "line": "27"}, "connection": "ticketgol", "explain": null, "start_percent": 23.273, "width_percent": 4.468}, {"sql": "select `id`, `event_id`, `ticket_no`, `price`, `sector_id`, `event_id` from `tickets` where `tickets`.`id` in (6, 8) and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 24}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.799613, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "OrderService.php:27", "source": {"index": 21, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=27", "ajax": false, "filename": "OrderService.php", "line": "27"}, "connection": "ticketgol", "explain": null, "start_percent": 27.74, "width_percent": 7.481}, {"sql": "select `id`, `date`, `time`, `timezone`, `category`, `country_id`, `home_club_id`, `guest_club_id`, `stadium_id`, `league_id` from `events` where `events`.`id` in (5, 7) and `events`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 24}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.8043709, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "OrderService.php:27", "source": {"index": 26, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=27", "ajax": false, "filename": "OrderService.php", "line": "27"}, "connection": "ticketgol", "explain": null, "start_percent": 35.221, "width_percent": 5.506}, {"sql": "select `id`, `event_id`, `name`, `description` from `event_translations` where `locale` = 'en' and `event_translations`.`event_id` in (5, 7)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 24}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 34, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.808295, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "OrderService.php:27", "source": {"index": 31, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=27", "ajax": false, "filename": "OrderService.php", "line": "27"}, "connection": "ticketgol", "explain": null, "start_percent": 40.727, "width_percent": 7.896}, {"sql": "select `sluggable_id`, `slug`, `locale` from `slugs` where `locale` = 'en' and `slugs`.`sluggable_id` in (5, 7) and `slugs`.`sluggable_type` = 'App\\\\Models\\\\Event'", "type": "query", "params": [], "bindings": ["en", "App\\Models\\Event"], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 24}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 34, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.8131318, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "OrderService.php:27", "source": {"index": 31, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=27", "ajax": false, "filename": "OrderService.php", "line": "27"}, "connection": "ticketgol", "explain": null, "start_percent": 48.623, "width_percent": 9.455}, {"sql": "select `id` from `stadium_sectors` where `stadium_sectors`.`id` in (10, 45) and `stadium_sectors`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 24}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.818306, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "OrderService.php:27", "source": {"index": 26, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=27", "ajax": false, "filename": "OrderService.php", "line": "27"}, "connection": "ticketgol", "explain": null, "start_percent": 58.078, "width_percent": 4.364}, {"sql": "select `stadium_sector_id`, `name` from `stadium_sector_translations` where `locale` = 'en' and `stadium_sector_translations`.`stadium_sector_id` in (10, 45)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 24}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 34, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.82188, "duration": 0.0029500000000000004, "duration_str": "2.95ms", "memory": 0, "memory_str": null, "filename": "OrderService.php:27", "source": {"index": 31, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=27", "ajax": false, "filename": "OrderService.php", "line": "27"}, "connection": "ticketgol", "explain": null, "start_percent": 62.442, "width_percent": 15.325}, {"sql": "select `id`, `order_id`, `name`, `email`, `gender`, `dob` from `attendees` where `attendees`.`order_id` in (11, 12)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 24}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.828504, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "OrderService.php:27", "source": {"index": 21, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=27", "ajax": false, "filename": "OrderService.php", "line": "27"}, "connection": "ticketgol", "explain": null, "start_percent": 77.766, "width_percent": 8.104}, {"sql": "select `id`, `order_id`, `status`, `currency_code`, `total_amount`, `paid_at`, `created_at` from `order_transactions` where `order_transactions`.`order_id` in (11, 12)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 24}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.8333058, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "OrderService.php:27", "source": {"index": 21, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=27", "ajax": false, "filename": "OrderService.php", "line": "27"}, "connection": "ticketgol", "explain": null, "start_percent": 85.87, "width_percent": 10.39}, {"sql": "update `sessions` set `payload` = 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiakxzVjRtb3lGbHd1NVJ2ckw2dDFPdDRMVUVoeDk5UlhobG9CWmo1UyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6NztzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJFYxd1I2NEdNUW9sVE1BcEUzd3pseWV3TDhQOWJzQUMzYWZaT2dJaGo5ZnJtQ3VxelkuLjVlIjt9', `last_activity` = **********, `user_id` = 7, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'lozOkcXmaVGi8C7rcRTIGiWNVnpFNp5J1rBvucUG'", "type": "query", "params": [], "bindings": ["YTo0OntzOjY6Il90b2tlbiI7czo0MDoiakxzVjRtb3lGbHd1NVJ2ckw2dDFPdDRMVUVoeDk5UlhobG9CWmo1UyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6NztzOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJFYxd1I2NEdNUW9sVE1BcEUzd3pseWV3TDhQOWJzQUMzYWZaT2dJaGo5ZnJtQ3VxelkuLjVlIjt9", **********, 7, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "lozOkcXmaVGi8C7rcRTIGiWNVnpFNp5J1rBvucUG"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.6016529, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 96.26, "width_percent": 3.74}]}, "models": {"data": {"App\\Models\\Order": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "App\\Models\\Ticket": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FTicket.php&line=1", "ajax": false, "filename": "Ticket.php", "line": "?"}}, "App\\Models\\Event": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\EventTranslation": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEventTranslation.php&line=1", "ajax": false, "filename": "EventTranslation.php", "line": "?"}}, "App\\Models\\Slug": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "App\\Models\\StadiumSector": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSector.php&line=1", "ajax": false, "filename": "StadiumSector.php", "line": "?"}}, "App\\Models\\StadiumSectorTranslation": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSectorTranslation.php&line=1", "ajax": false, "filename": "StadiumSectorTranslation.php", "line": "?"}}, "App\\Models\\Attendee": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FAttendee.php&line=1", "ajax": false, "filename": "Attendee.php", "line": "?"}}, "App\\Models\\OrderTransaction": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FOrderTransaction.php&line=1", "ajax": false, "filename": "OrderTransaction.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 19, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jLsV4moyFlwu5RvrL6t1Ot4LUEhx99RXhloBZj5S", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "password_hash_web": "$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f2e3fe0-1913-49a6-a9e7-f1ef38c3c0a1\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/orders", "status_code": "<pre class=sf-dump id=sf-dump-1488044816 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-1488044816\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1410380787 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1410380787\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-413241168 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_to</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-413241168\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1960852870 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"796 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; XSRF-TOKEN=eyJpdiI6ImdldWdmbjZJQTRNS2Z2d0xtQlY0U2c9PSIsInZhbHVlIjoia0l0cnZLZDg3VDd3REJLZlIweFBqQjhkQTh0N3RCcEttUWxsM2VaN29ySkhvdXcvOFdIN3VnN1pxRFB5cUVibk1HaXpHZkJVcTJjejdob2hBYWoreFQvdjRmWllPZ2FmdW1YZ1BLbHp2RFlZNkhaa3hYazN4MTZ6ZXVsQVQ1ZVciLCJtYWMiOiIxNWVmYjlhY2JmNjc4YTAwNzYxMDQwOWNhMWY5NjBlZDI1ZDc0YWU2MDE2MzNjYWZiNzExMDUwNTU0MDkzMGU1IiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6InFSZHY1ZVRrem5xeGhWU1YwR3FmcVE9PSIsInZhbHVlIjoiT1FDR0NQWnhtQjZWQVNHYkQwWm12Nzh0OVNJTnNsTWxjTFAxa3YyRUlwS2V3NTM0M1VpQmU2RjhVN0N0R3l3VXNFaklmRTN0RHM1TU54cHQxaXRSMHd5ZVJvNXFUVTc2WHFHbWZnaGdybUxWY0JHWE5BaW9KcVM5TzB1enViREciLCJtYWMiOiJhOWE0MDI4ZDk4Y2JmODg2YjVmMTkxYWNmZjk5ZDMxNWI2ZGQ3MmIyMTBlZmIzNTM1NGE2N2M0NjIyNDI3OTI1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://ticketgol.test/my-account/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://ticketgol.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImdldWdmbjZJQTRNS2Z2d0xtQlY0U2c9PSIsInZhbHVlIjoia0l0cnZLZDg3VDd3REJLZlIweFBqQjhkQTh0N3RCcEttUWxsM2VaN29ySkhvdXcvOFdIN3VnN1pxRFB5cUVibk1HaXpHZkJVcTJjejdob2hBYWoreFQvdjRmWllPZ2FmdW1YZ1BLbHp2RFlZNkhaa3hYazN4MTZ6ZXVsQVQ1ZVciLCJtYWMiOiIxNWVmYjlhY2JmNjc4YTAwNzYxMDQwOWNhMWY5NjBlZDI1ZDc0YWU2MDE2MzNjYWZiNzExMDUwNTU0MDkzMGU1IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">53</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1960852870\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1000537327 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jLsV4moyFlwu5RvrL6t1Ot4LUEhx99RXhloBZj5S</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lozOkcXmaVGi8C7rcRTIGiWNVnpFNp5J1rBvucUG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1000537327\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1901973451 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 18 Jun 2025 01:24:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ill2TWswY3NXdlJZcTZDL0Z0dC94ZUE9PSIsInZhbHVlIjoiaGQ0VWw1QUpVb3k4U20yK2J2MGRKRjZNd0hxbUg0bC8zemFOTHFqYlI3MUozUlc5bWxmNDlOZ2IvWGhOQW4xNmlKaTZjMGZmR0lVdjBmUER0Wk8yaWh2OEtGZHB3YVJxL3p4MzJHSDMzOTU2Q3FTQ0s0bFRiT2NUUlh3emJhN08iLCJtYWMiOiJlYmUxN2JkYmJiMWJmMzM2ZDc5YTVmMWRhNGM2ZGQyZWQ2MWM3OWM3N2M0MzQ2YTk1MzFiOTU1YzQ5MGRiZjI2IiwidGFnIjoiIn0%3D; expires=Wed, 18 Jun 2025 03:24:49 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6Iko5UkFqeU9NRGQ5Q1BSMDdNNGVEM3c9PSIsInZhbHVlIjoiUVl1RGwzT2RUOEFWMUNnQUVxSjdLcExRY0hpVUNHYTdJUEV5aWYvT2tZNFdMbmYvOG40SFVBemdjYkJNR3VlZ0RNdFZSZCtsWVZxZ0YxQXV2OGdxNEtHemt6MUN6eEt0Z1czZmdhVFBPa0ZnK1BVdmhUcXc5Y1VLSlIvYVJwSmEiLCJtYWMiOiI5NjhjNjMzZjNmNDg0NGU5ZjQ2NzZkNTg4ZTFhZDM3NjViODQ4NTY3MGRhNDdiNmNhNzVkOWU5MTY1NDZlN2Q3IiwidGFnIjoiIn0%3D; expires=Wed, 18 Jun 2025 03:24:49 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ill2TWswY3NXdlJZcTZDL0Z0dC94ZUE9PSIsInZhbHVlIjoiaGQ0VWw1QUpVb3k4U20yK2J2MGRKRjZNd0hxbUg0bC8zemFOTHFqYlI3MUozUlc5bWxmNDlOZ2IvWGhOQW4xNmlKaTZjMGZmR0lVdjBmUER0Wk8yaWh2OEtGZHB3YVJxL3p4MzJHSDMzOTU2Q3FTQ0s0bFRiT2NUUlh3emJhN08iLCJtYWMiOiJlYmUxN2JkYmJiMWJmMzM2ZDc5YTVmMWRhNGM2ZGQyZWQ2MWM3OWM3N2M0MzQ2YTk1MzFiOTU1YzQ5MGRiZjI2IiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 03:24:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6Iko5UkFqeU9NRGQ5Q1BSMDdNNGVEM3c9PSIsInZhbHVlIjoiUVl1RGwzT2RUOEFWMUNnQUVxSjdLcExRY0hpVUNHYTdJUEV5aWYvT2tZNFdMbmYvOG40SFVBemdjYkJNR3VlZ0RNdFZSZCtsWVZxZ0YxQXV2OGdxNEtHemt6MUN6eEt0Z1czZmdhVFBPa0ZnK1BVdmhUcXc5Y1VLSlIvYVJwSmEiLCJtYWMiOiI5NjhjNjMzZjNmNDg0NGU5ZjQ2NzZkNTg4ZTFhZDM3NjViODQ4NTY3MGRhNDdiNmNhNzVkOWU5MTY1NDZlN2Q3IiwidGFnIjoiIn0%3D; expires=Wed, 18-Jun-2025 03:24:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1901973451\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-669477678 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jLsV4moyFlwu5RvrL6t1Ot4LUEhx99RXhloBZj5S</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$V1wR64GMQolTMApE3wzlyewL8P9bsAC3afZOgIhj9frmCuqzY..5e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-669477678\", {\"maxDepth\":0})</script>\n"}}