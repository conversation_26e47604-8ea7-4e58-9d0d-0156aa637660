<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'order_no' => $this->order_no,
            'total_price' => $this->total_price,
            'quantity' => $this->quantity,
            'status' => [
                'value' => $this->status->value,
                'label' => $this->status->getLabel(),
                'color' => $this->status->getBadgeColour(),
            ],
            'purchase_date' => $this->purchase_date,
            'created_at' => $this->created_at,
            'ticket' => [
                'id' => $this->whenLoaded('ticket', fn () => $this->ticket->id),
                'ticket_no' => $this->whenLoaded('ticket', fn () => $this->ticket->ticket_no),
                'price' => $this->whenLoaded('ticket', fn () => $this->ticket->price),
                'ticket_type' => $this->whenLoaded('ticket', fn () => $this->ticket->ticket_type),
                'face_value' => $this->whenLoaded('ticket', fn () => $this->ticket->face_value_price),
                'row' => $this->whenLoaded('ticket', fn () => $this->ticket->ticket_rows),
                'seat' => $this->whenLoaded('ticket', fn () => $this->ticket->ticket_seats),
                'sector' => $this->whenLoaded('ticket', function () {
                    if ($this->ticket->sector) {
                        return [
                            'id' => $this->ticket->sector->id,
                            'name' => $this->ticket->sector->translation?->name ?? '',
                        ];
                    }
                    return null;
                }),
                'restrictions' => $this->whenLoaded('ticket', function () {
                    if ($this->ticket->restrictions) {
                        return $this->ticket->restrictions->map(function ($restriction) {
                             return [
                                 'id' => $restriction->id,
                                 'type' => $restriction->type,
                                 'name' => $restriction->translation?->name ?? '',
                             ];
                         });
                    }
                    return [];
                }),
                'event' => $this->whenLoaded('ticket', function () {
                    if ($this->ticket->event) {
                        $firstImage = $this->ticket->event->media->first();

                        return [
                            'id' => $this->ticket->event->id,
                            'name' => $this->ticket->event->translation?->name ?? '',
                            'description' => $this->ticket->event->translation?->description ?? '',
                            'date' => $this->ticket->event->date,
                            'time' => $this->ticket->event->time,
                            'timezone' => $this->ticket->event->timezone,
                            'category' => $this->ticket->event->category,
                            'image' => $firstImage?->getUrl() ?? '',
                            'image_alt' => $firstImage?->custom_properties['alt'] ?? '',
                            'country' => $this->ticket->event->country ? [
                                'id' => $this->ticket->event->country->id,
                                'name' => $this->ticket->event->country->translation?->name ?? '',
                            ] : null,
                            'home_club' => $this->ticket->event->homeClub ? [
                                'id' => $this->ticket->event->homeClub->id,
                                'name' => $this->ticket->event->homeClub->translation?->name ?? '',
                            ] : null,
                            'guest_club' => $this->ticket->event->guestClub ? [
                                'id' => $this->ticket->event->guestClub->id,
                                'name' => $this->ticket->event->guestClub->translation?->name ?? '',
                            ] : null,
                            'stadium' => $this->ticket->event->stadium ? [
                                'id' => $this->ticket->event->stadium->id,
                                'name' => $this->ticket->event->stadium->translation?->name ?? '',
                                'address_line_1' => $this->ticket->event->stadium->address_line_1,
                                'address_line_2' => $this->ticket->event->stadium->address_line_2,
                                'postcode' => $this->ticket->event->stadium->postcode,
                                'country' => $this->ticket->event->stadium->country ? [
                                    'id' => $this->ticket->event->stadium->country->id,
                                    'name' => $this->ticket->event->stadium->country->translation?->name ?? '',
                                ] : null,
                            ] : null,
                            'league' => $this->ticket->event->league ? [
                                'id' => $this->ticket->event->league->id,
                                'name' => $this->ticket->event->league->translation?->name ?? '',
                            ] : null,
                            'restrictions' => $this->ticket->event->restrictions ? $this->ticket->event->restrictions->map(function ($restriction) {
                                 return [
                                     'id' => $restriction->id,
                                     'type' => $restriction->type,
                                     'name' => $restriction->translation?->name ?? '',
                                 ];
                             }) : [],
                        ];
                    }

                    return null;
                }),
            ],
        ];
    }

    /**
     * Customize the pagination information for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  array  $paginated
     * @param  array  $default
     * @return array
     */
    public function paginationInformation($request, $paginated, $default)
    {
        return [
            'meta' => [
                'current_page' => $paginated['meta']['current_page'],
                'last_page' => $paginated['meta']['last_page'],
                'per_page' => $paginated['meta']['per_page'],
                'total' => $paginated['meta']['total'],
                'next_page_url' => $paginated['meta']['next_page_url'],
                'prev_page_url' => $paginated['meta']['prev_page_url'],
            ],
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function with($request)
    {
        // This only works when the resource is the outermost resource
        return [
            'meta' => [
                'current_page' => $this->resource->currentPage(),
                'last_page' => $this->resource->lastPage(),
                'per_page' => $this->resource->perPage(),
                'total' => $this->resource->total(),
                'next_page_url' => $this->resource->nextPageUrl(),
                'prev_page_url' => $this->resource->previousPageUrl(),
            ],
        ];
    }
}
