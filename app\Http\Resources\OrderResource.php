<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'order_no' => $this->order_no,
            'total_price' => $this->total_price,
            'quantity' => $this->quantity,
            'status' => [
                'value' => $this->status->value,
                'label' => $this->status->getLabel(),
                'color' => $this->status->getBadgeColour(),
            ],
            'purchase_date' => $this->purchase_date,
            'created_at' => $this->created_at,
            'ticket' => [
                'ticket_no' => $this->whenLoaded('ticket', fn () => $this->ticket->ticket_no),
                'sector' => $this->whenLoaded('ticket', function () {
                    if ($this->ticket->sector) {
                        return [
                            'name' => $this->ticket->sector->translation?->name ?? '',
                        ];
                    }
                    return null;
                }),
                'event' => $this->whenLoaded('ticket', function () {
                    if ($this->ticket->event) {
                        return [
                            'name' => $this->ticket->event->translation?->name ?? '',
                            'date' => $this->ticket->event->date,
                            'time' => $this->ticket->event->time,
                            'stadium' => $this->ticket->event->stadium ? [
                                'name' => $this->ticket->event->stadium->translation?->name ?? '',
                            ] : null,
                            'home_club' => $this->ticket->event->homeClub ? [
                                'name' => $this->ticket->event->homeClub->translation?->name ?? '',
                            ] : null,
                            'guest_club' => $this->ticket->event->guestClub ? [
                                'name' => $this->ticket->event->guestClub->translation?->name ?? '',
                            ] : null,
                        ];
                    }
                    return null;
                }),
            ],
        ];
    }
}
