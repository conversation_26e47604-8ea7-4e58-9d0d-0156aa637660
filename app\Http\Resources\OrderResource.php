<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'order_no' => $this->order_no,
            'total_price' => $this->total_price,
            'quantity' => $this->quantity,
            'status' => [
                'value' => $this->status->value,
                'label' => $this->status->getLabel(),
                'color' => $this->status->getBadgeColour(),
            ],
            'purchase_date' => $this->purchase_date,
            'created_at' => $this->created_at,
            'ticket' => [
                'id' => $this->whenLoaded('ticket', fn () => $this->ticket->id),
                'event_id' => $this->whenLoaded('ticket', fn () => $this->ticket->event_id),
                'ticket_no' => $this->whenLoaded('ticket', fn () => $this->ticket->ticket_no),
                'price' => $this->whenLoaded('ticket', fn () => $this->ticket->price),
                'sector_id' => $this->whenLoaded('ticket', fn () => $this->ticket->sector_id),
                'sector' => $this->whenLoaded('ticket', function () {
                    if ($this->ticket->sector) {
                        return [
                            'translation' => [
                                'name' => $this->ticket->sector->translation?->name ?? '',
                            ],
                        ];
                    }
                    return null;
                }),
                'event' => $this->whenLoaded('ticket', function () {
                    if ($this->ticket->event) {
                        return [
                            'id' => $this->ticket->event->id,
                            'date' => $this->ticket->event->date,
                            'time' => $this->ticket->event->time,
                            'timezone' => $this->ticket->event->timezone,
                            'category' => $this->ticket->event->category,
                            'country_id' => $this->ticket->event->country_id,
                            'home_club_id' => $this->ticket->event->home_club_id,
                            'guest_club_id' => $this->ticket->event->guest_club_id,
                            'stadium_id' => $this->ticket->event->stadium_id,
                            'league_id' => $this->ticket->event->league_id,
                            'name' => $this->ticket->event->translation?->name ?? '',
                            'description' => $this->ticket->event->translation?->description ?? '',
                            'stadium' => $this->ticket->event->stadium ? [
                                'id' => $this->ticket->event->stadium->id,
                                'address_line_1' => $this->ticket->event->stadium->address_line_1,
                                'address_line_2' => $this->ticket->event->stadium->address_line_2,
                                'postcode' => $this->ticket->event->stadium->postcode,
                                'country_id' => $this->ticket->event->stadium->country_id,
                                'name' => $this->ticket->event->stadium->translation?->name ?? '',
                                'country' => $this->ticket->event->stadium->country ? [
                                    'translation' => [
                                        'name' => $this->ticket->event->stadium->country->translation?->name ?? '',
                                    ],
                                ] : null,
                            ] : null,
                            'league' => $this->ticket->event->league ? [
                                'id' => $this->ticket->event->league->id,
                                'translation' => [
                                    'name' => $this->ticket->event->league->translation?->name ?? '',
                                ],
                            ] : null,
                            'home_club' => $this->ticket->event->homeClub ? [
                                'id' => $this->ticket->event->homeClub->id,
                                'translation' => [
                                    'name' => $this->ticket->event->homeClub->translation?->name ?? '',
                                ],
                            ] : null,
                            'guest_club' => $this->ticket->event->guestClub ? [
                                'id' => $this->ticket->event->guestClub->id,
                                'translation' => [
                                    'name' => $this->ticket->event->guestClub->translation?->name ?? '',
                                ],
                            ] : null,
                        ];
                    }

                    return null;
                }),
            ],
        ];
    }
}
