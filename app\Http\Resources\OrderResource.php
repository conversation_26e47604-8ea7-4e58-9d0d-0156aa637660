<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'order_no' => $this->order_no,
            'total_price' => $this->total_price,
            'quantity' => $this->quantity,
            'status' => [
                'value' => $this->status->value,
                'label' => $this->status->getLabel(),
                'color' => $this->status->getBadgeColour(),
            ],
            'purchase_date' => $this->purchase_date,
            'created_at' => $this->created_at,
            'ticket' => [
                'id' => $this->whenLoaded('ticket', fn () => $this->ticket->id),
                'ticket_no' => $this->whenLoaded('ticket', fn () => $this->ticket->ticket_no),
                'ticket_type' => $this->whenLoaded('ticket', fn () => $this->ticket->ticket_type->getLabel()),
                'price' => $this->whenLoaded('ticket', fn () => $this->ticket->price),
                'face_value_price' => $this->whenLoaded('ticket', fn () => $this->ticket->face_value_price),
                'ticket_rows' => $this->whenLoaded('ticket', fn () => $this->ticket->ticket_rows),
                'ticket_seats' => $this->whenLoaded('ticket', fn () => $this->ticket->ticket_seats),
                'sector' => $this->whenLoaded('ticket', function () {
                    if ($this->ticket->sector) {
                        return [
                            'id' => $this->ticket->sector->id,
                            'name' => $this->ticket->sector->translation?->name ?? '',
                        ];
                    }
                    return null;
                }),
                'event' => $this->whenLoaded('ticket', function () {
                    if ($this->ticket->event) {
                        return [
                            'id' => $this->ticket->event->id,
                            'date' => $this->ticket->event->date,
                            'time' => $this->ticket->event->time,
                            'name' => $this->ticket->event->translation?->name ?? '',
                            'stadium' => $this->ticket->event->stadium ? [
                                'id' => $this->ticket->event->stadium->id,
                                'address_line_1' => $this->ticket->event->stadium->address_line_1,
                                'address_line_2' => $this->ticket->event->stadium->address_line_2,
                                'postcode' => $this->ticket->event->stadium->postcode,
                                'name' => $this->ticket->event->stadium->translation?->name ?? '',
                                'country' => $this->ticket->event->stadium->country ? [
                                    'id' => $this->ticket->event->stadium->country->id,
                                    'name' => $this->ticket->event->stadium->country->translation?->name ?? '',
                                ] : null,
                            ] : null,
                            'league' => $this->ticket->event->league ? [
                                'id' => $this->ticket->event->league->id,
                                'name' => $this->ticket->event->league->translation?->name ?? '',
                            ] : null,
                            'home_club' => $this->ticket->event->homeClub ? [
                                'id' => $this->ticket->event->homeClub->id,
                                'name' => $this->ticket->event->homeClub->translation?->name ?? '',
                            ] : null,
                            'guest_club' => $this->ticket->event->guestClub ? [
                                'id' => $this->ticket->event->guestClub->id,
                                'name' => $this->ticket->event->guestClub->translation?->name ?? '',
                            ] : null,
                        ];
                    }
                    return null;
                }),
            ],
        ];
    }
}
