{"__meta": {"id": "Xa8eb35bb9ba400bf54c876823743d248", "datetime": "2025-06-18 01:17:22", "utime": 1750209442.98139, "method": "GET", "uri": "/livewire/livewire.js?id=38dc8241", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750209442.858916, "end": 1750209442.981409, "duration": 0.12249302864074707, "duration_str": "122ms", "measures": [{"label": "Booting", "start": 1750209442.858916, "relative_start": 0, "end": 1750209442.916242, "relative_end": 1750209442.916242, "duration": 0.05732583999633789, "duration_str": "57.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750209442.916257, "relative_start": 0.05734086036682129, "end": 1750209442.981413, "relative_end": 3.814697265625e-06, "duration": 0.0651559829711914, "duration_str": "65.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 4835464, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET livewire/livewire.js", "controller": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile", "as": "generated::pXBTwczdMKDrzcvZ", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" onclick=\"\">vendor/livewire/livewire/src/Mechanisms/FrontendAssets/FrontendAssets.php:78-85</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://admin.ticketgol.test/_debugbar/telescope/9f2e3d36-9117-453c-b0fe-f0a6c28819bb\" target=\"_blank\">View in Telescope</a>", "path_info": "/livewire/livewire.js", "status_code": "<pre class=sf-dump id=sf-dump-518006120 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-518006120\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/javascript; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2071824565 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"8 characters\">38dc8241</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2071824565\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1093773288 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1093773288\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1253171498 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1090 characters\">selected_locale=en; filament_language_switch_locale=eyJpdiI6IlFoT3lkSS9LbGtzcUhWWGNYNzZjNVE9PSIsInZhbHVlIjoiN3B5YjBONGFhdmJaNVNSNlpPd0UydXhhdUw4RVdxam5WckYzSytWdXdlVkZaVjhGUGJtUWZicXZER0tvdVVTdiIsIm1hYyI6ImNmYjUyMWUyZTJlNTYxMjllYmU0NjJkMzgzZWFlOTRhZjViMTBmZmE3YWI4ZmQ0MjAyNmM5YzQ3OGY5MzcyN2YiLCJ0YWciOiIifQ%3D%3D; ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; XSRF-TOKEN=eyJpdiI6ImY0QjRXWmdzajI3QnpkZjM3REpDQmc9PSIsInZhbHVlIjoiaUZRMVp1UjJkMFRYTU5tSE44UFg2bjRJY0lKK1lIUmJnMFFBZlQxNVN5SHBVOVY2WmFFZHFsNDR5c2JmcTExdklHdXBVOTExR0pyYkt2c2hmMGxqZGMremcxa3plZGt6TUZGMmIzRThPWDgxL0VueVhyNndCTHFGVitKRkd0bkQiLCJtYWMiOiJmNzg5YjliMGY4YThhMDYyNjdhYTQ2Y2VhYzRjN2JhNDAwNTc1NjAwNzYyZjE2MTljOGIzYWY2NzExOGVmOWQwIiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6Imt5ZnJ6bjlVU2NzREVVdjJvQ1RGQUE9PSIsInZhbHVlIjoiVFBvUFJiTGs4QTBQaU03K0tRZjNLRXRtMnlDYURhWEJ2ajJmdmlBQVc1aXc2MnNVZ0V2YXJPL2Y2cFMzdmN3ZjZva3J3Ui9wT2dkdnJySk5QOFh6dXRpSDdlOG1iUnptVXAreHZGVXMyakIrZVV6a1lrWG1TQWc0SG5zS3hSdWEiLCJtYWMiOiIyNGM5OTUzZmE3Mjg2OTI0NTlkNzBkMGJlZDc3NDAxMTAzZThmMWI3OWQ2ZDJkNTg5YTdjYjkxOGM3ZjQ3MWM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://admin.ticketgol.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">admin.ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1253171498\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1749644553 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>filament_language_switch_locale</span>\" => \"<span class=sf-dump-str title=\"256 characters\">eyJpdiI6IlFoT3lkSS9LbGtzcUhWWGNYNzZjNVE9PSIsInZhbHVlIjoiN3B5YjBONGFhdmJaNVNSNlpPd0UydXhhdUw4RVdxam5WckYzSytWdXdlVkZaVjhGUGJtUWZicXZER0tvdVVTdiIsIm1hYyI6ImNmYjUyMWUyZTJlNTYxMjllYmU0NjJkMzgzZWFlOTRhZjViMTBmZmE3YWI4ZmQ0MjAyNmM5YzQ3OGY5MzcyN2YiLCJ0YWciOiIifQ==</span>\"\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => \"<span class=sf-dump-str title=\"38 characters\">&quot;05cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e&quot;</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImY0QjRXWmdzajI3QnpkZjM3REpDQmc9PSIsInZhbHVlIjoiaUZRMVp1UjJkMFRYTU5tSE44UFg2bjRJY0lKK1lIUmJnMFFBZlQxNVN5SHBVOVY2WmFFZHFsNDR5c2JmcTExdklHdXBVOTExR0pyYkt2c2hmMGxqZGMremcxa3plZGt6TUZGMmIzRThPWDgxL0VueVhyNndCTHFGVitKRkd0bkQiLCJtYWMiOiJmNzg5YjliMGY4YThhMDYyNjdhYTQ2Y2VhYzRjN2JhNDAwNTc1NjAwNzYyZjE2MTljOGIzYWY2NzExOGVmOWQwIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Imt5ZnJ6bjlVU2NzREVVdjJvQ1RGQUE9PSIsInZhbHVlIjoiVFBvUFJiTGs4QTBQaU03K0tRZjNLRXRtMnlDYURhWEJ2ajJmdmlBQVc1aXc2MnNVZ0V2YXJPL2Y2cFMzdmN3ZjZva3J3Ui9wT2dkdnJySk5QOFh6dXRpSDdlOG1iUnptVXAreHZGVXMyakIrZVV6a1lrWG1TQWc0SG5zS3hSdWEiLCJtYWMiOiIyNGM5OTUzZmE3Mjg2OTI0NTlkNzBkMGJlZDc3NDAxMTAzZThmMWI3OWQ2ZDJkNTg5YTdjYjkxOGM3ZjQ3MWM0IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1749644553\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-421287714 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">application/javascript; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 18 Jun 2026 01:17:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Oct 2024 19:35:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 18 Jun 2025 01:17:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">340160</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-421287714\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1014915611 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1014915611\", {\"maxDepth\":0})</script>\n"}}